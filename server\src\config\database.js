/**
 * 数据库配置模块
 * 统一管理数据库连接和配置
 */

const mongoose = require('mongoose');

/**
 * 数据库配置
 */
const DATABASE_CONFIG = {
  development: {
    uri: process.env.MONGODB_URI_DEV || 'mongodb://localhost:27017/aitools',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 300000, // 5分钟超时
    }
  },

  production: {
    uri: process.env.MONGODB_URI || '*************************************************************',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 20,
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 300000, // 5分钟超时
      retryWrites: true,
      w: 'majority'
    }
  }
};

/**
 * 获取当前环境
 */
function getEnvironment() {
  return process.env.NODE_ENV || 'development';
}

/**
 * 获取数据库配置
 */
function getDatabaseConfig() {
  const env = getEnvironment();
  return DATABASE_CONFIG[env] || DATABASE_CONFIG.development;
}

/**
 * 连接数据库
 */
async function connectDatabase() {
  try {
    const config = getDatabaseConfig();
    const env = getEnvironment();
    
    console.log(`🔗 正在连接数据库 (${env})...`);
    
    await mongoose.connect(config.uri, config.options);
    
    console.log('✅ MongoDB 连接成功');
    console.log(`📊 数据库: ${mongoose.connection.name}`);
    console.log(`🌐 主机: ${mongoose.connection.host}:${mongoose.connection.port}`);
    
    // 监听连接事件
    mongoose.connection.on('error', (error) => {
      console.error('❌ MongoDB 连接错误:', error);
    });
    
    mongoose.connection.on('disconnected', () => {
      console.warn('⚠️ MongoDB 连接断开');
    });
    
    mongoose.connection.on('reconnected', () => {
      console.log('🔄 MongoDB 重新连接成功');
    });
    
    return mongoose.connection;
  } catch (error) {
    console.error('❌ MongoDB 连接失败:', error);
    
    if (getEnvironment() === 'production') {
      // 生产环境下数据库连接失败应该退出
      process.exit(1);
    } else {
      // 开发环境下继续运行，但记录警告
      console.log('⚠️ 服务器将继续运行，但数据库功能不可用');
      return null; // 返回 null 而不是抛出错误
    }
  }
}

/**
 * 断开数据库连接
 */
async function disconnectDatabase() {
  try {
    await mongoose.disconnect();
    console.log('✅ 数据库连接已断开');
  } catch (error) {
    console.error('❌ 断开数据库连接失败:', error);
    throw error;
  }
}

/**
 * 检查数据库连接状态
 */
function getDatabaseStatus() {
  const state = mongoose.connection.readyState;
  const states = {
    0: 'disconnected',
    1: 'connected',
    2: 'connecting',
    3: 'disconnecting'
  };
  
  return {
    state: states[state] || 'unknown',
    name: mongoose.connection.name,
    host: mongoose.connection.host,
    port: mongoose.connection.port
  };
}

/**
 * 数据库健康检查
 */
async function healthCheck() {
  try {
    const status = getDatabaseStatus();
    
    if (status.state !== 'connected') {
      return {
        healthy: false,
        status: status.state,
        message: '数据库未连接'
      };
    }
    
    // 执行简单的查询测试连接
    await mongoose.connection.db.admin().ping();
    
    return {
      healthy: true,
      status: status.state,
      database: status.name,
      host: `${status.host}:${status.port}`,
      message: '数据库连接正常'
    };
  } catch (error) {
    return {
      healthy: false,
      status: 'error',
      message: error.message
    };
  }
}

/**
 * 优雅关闭数据库连接
 */
function setupGracefulShutdown() {
  process.on('SIGINT', async () => {
    console.log('\n🛑 收到 SIGINT 信号，正在关闭数据库连接...');
    try {
      await disconnectDatabase();
      process.exit(0);
    } catch (error) {
      console.error('❌ 关闭数据库连接失败:', error);
      process.exit(1);
    }
  });
  
  process.on('SIGTERM', async () => {
    console.log('\n🛑 收到 SIGTERM 信号，正在关闭数据库连接...');
    try {
      await disconnectDatabase();
      process.exit(0);
    } catch (error) {
      console.error('❌ 关闭数据库连接失败:', error);
      process.exit(1);
    }
  });
}

module.exports = {
  connectDatabase,
  disconnectDatabase,
  getDatabaseStatus,
  healthCheck,
  setupGracefulShutdown,
  getEnvironment,
  getDatabaseConfig
};
