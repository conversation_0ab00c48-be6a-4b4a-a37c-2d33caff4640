const { ipcRenderer } = require('electron');

// 国际化文本
let i18nTexts = {};

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', async () => {
    // 加载国际化文本
    await loadI18nTexts();

    // 加载并应用主题
    await loadAndApplyTheme();
});

// 监听语言更改事件
ipcRenderer.on('language-changed', async (event, newLanguage) => {
    console.log('收到语言更改通知:', newLanguage);
    await loadI18nTexts();
});

// 监听主题更改事件
ipcRenderer.on('theme-changed', (event, theme) => {
    console.log('主界面收到主题更改通知:', theme);
    applyTheme(theme);
});

// 监听设置更改事件（特别是自动更新设置）
ipcRenderer.on('settings-changed', async (event, changedSettings) => {
    console.log('主界面收到设置更改通知:', changedSettings);

    // 如果自动更新设置发生变化
    if (changedSettings.hasOwnProperty('autoUpdate')) {
        autoUpdateEnabled = changedSettings.autoUpdate;
        console.log('自动更新设置已更新:', autoUpdateEnabled);

        // 根据新设置调整界面
        if (autoUpdateEnabled !== false) {
            // 开启自动更新时，隐藏新版本标签
            hideNewVersionTag();
        } else if (currentUpdateInfo) {
            // 关闭自动更新时，如果有可用更新则显示标签
            showNewVersionTag(currentUpdateInfo);
        }
    }
});

// 加载并应用主题
async function loadAndApplyTheme() {
    try {
        const theme = await ipcRenderer.invoke('get-setting', 'theme') || 'auto';
        applyTheme(theme);
    } catch (error) {
        console.error('加载主题失败:', error);
        applyTheme('auto');
    }
}
// ===== 更新提示与进度（使用共享模块）=====
const { getUpdateCard } = require('../utils/update-card');

// 更新相关状态
let currentUpdateInfo = null;
let autoUpdateEnabled = true;
let isDownloading = false;

// 初始化更新卡片和新版本标签
document.addEventListener('DOMContentLoaded', async () => {
    getUpdateCard(); // 初始化更新卡片实例

    // 获取自动更新设置
    try {
        autoUpdateEnabled = await ipcRenderer.invoke('get-setting', 'autoUpdate');
        console.log('自动更新设置:', autoUpdateEnabled);
    } catch (e) {
        console.warn('获取自动更新设置失败:', e);
        autoUpdateEnabled = true;
    }

    // 检查是否有待处理的更新状态
    checkExistingUpdateState();
});

// 监听更新相关事件
ipcRenderer.on('update-available', (event, info) => {
    console.log('主界面收到更新可用通知:', info);
    currentUpdateInfo = info;

    // 只有在关闭自动更新时才显示新版本标签
    if (autoUpdateEnabled === false) {
        showNewVersionTag(info);
    }
});

ipcRenderer.on('update-download-progress', (event, progress) => {
    console.log('主界面收到下载进度:', progress);

    // 如果是手动下载（自动更新关闭），更新标签状态
    if (autoUpdateEnabled === false && isDownloading) {
        updateNewVersionTagProgress(progress);
    }
});

ipcRenderer.on('update-downloaded', (event, info) => {
    console.log('主界面收到下载完成通知:', info);
    isDownloading = false;

    // 如果是手动下载，隐藏标签（由update-card处理后续逻辑）
    if (autoUpdateEnabled === false) {
        hideNewVersionTag();
    }
});

ipcRenderer.on('update-error', (event, error) => {
    console.log('主界面收到更新错误:', error);
    isDownloading = false;

    if (autoUpdateEnabled === false) {
        resetNewVersionTag();
    }
});




// 应用主题
function applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    console.log('主界面主题已应用:', theme);
}

// 加载国际化文本
async function loadI18nTexts() {
    try {
        const language = await ipcRenderer.invoke('get-setting', 'language') || 'zh-CN';
        console.log('主界面加载语言:', language);

        i18nTexts = await ipcRenderer.invoke('get-i18n-texts', language);
        console.log('主界面获取到的i18nTexts:', Object.keys(i18nTexts).slice(0, 10));
        console.log('app.title文本:', i18nTexts['app.title']);

        updateI18nTexts();
    } catch (error) {
        console.error('加载国际化文本失败:', error);
    }
}

// 更新界面文本
function updateI18nTexts() {
    console.log('正在更新界面文本，当前i18nTexts:', Object.keys(i18nTexts).slice(0, 5));

    document.querySelectorAll('[data-i18n]').forEach(element => {
        const key = element.getAttribute('data-i18n');
        const text = getNestedValue(i18nTexts, key);

        if (text) {
            console.log(`更新文本: ${key} -> ${text}`);
            element.textContent = text;
        } else {
            console.warn(`未找到翻译文本: ${key}`);
        }
    });
}

// 获取嵌套对象的值
function getNestedValue(obj, key) {
    return key.split('.').reduce((current, keyPart) => {
        return current && current[keyPart] !== undefined ? current[keyPart] : undefined;
    }, obj);
}

// ===== 新版本标签相关函数 =====

// 检查现有的更新状态
async function checkExistingUpdateState() {
    try {
        const state = await ipcRenderer.invoke('get-update-state');
        if (state && state.available && state.info && autoUpdateEnabled === false) {
            currentUpdateInfo = state.info;
            showNewVersionTag(state.info);

            if (state.downloading) {
                isDownloading = true;
                updateNewVersionTagProgress(state.progress || { percent: 0 });
            }
        }
    } catch (e) {
        console.warn('检查更新状态失败:', e);
    }
}

// 显示新版本标签
function showNewVersionTag(info) {
    const tag = document.getElementById('newVersionTag');
    const text = document.getElementById('newVersionText');

    if (tag && text) {
        text.textContent = `🚀 新版本 v${info.version} 可用`;
        tag.style.display = 'block';
        tag.className = 'new-version-tag';
    }
}

// 更新新版本标签的下载进度
function updateNewVersionTagProgress(progress) {
    const tag = document.getElementById('newVersionTag');
    const text = document.getElementById('newVersionText');

    if (tag && text) {
        const percent = Math.round(progress.percent || 0);
        text.textContent = `📥 下载中 ${percent}%`;
        tag.className = 'new-version-tag downloading';
    }
}

// 重置新版本标签（下载失败时）
function resetNewVersionTag() {
    const tag = document.getElementById('newVersionTag');
    const text = document.getElementById('newVersionText');

    if (tag && text && currentUpdateInfo) {
        text.textContent = `🚀 新版本 v${currentUpdateInfo.version} 可用`;
        tag.className = 'new-version-tag';
    }
}

// 隐藏新版本标签
function hideNewVersionTag() {
    const tag = document.getElementById('newVersionTag');
    if (tag) {
        tag.style.display = 'none';
    }
}

// 处理新版本标签点击
async function handleNewVersionClick() {
    if (isDownloading) {
        console.log('正在下载中，忽略点击');
        return;
    }

    if (!currentUpdateInfo) {
        console.log('没有可用的更新信息');
        return;
    }

    console.log('用户点击新版本标签，开始下载');
    isDownloading = true;

    try {
        const result = await ipcRenderer.invoke('start-update-download');
        if (!result || !result.success) {
            console.error('启动下载失败:', result?.error);
            isDownloading = false;
            resetNewVersionTag();
        }
    } catch (e) {
        console.error('启动下载失败:', e);
        isDownloading = false;
        resetNewVersionTag();
    }
}







// 打开管理面板
async function openAdminPanel() {
    const statusElement = document.getElementById('status');
    statusElement.textContent = getNestedValue(i18nTexts, 'main.status.opening_admin') || '正在打开管理面板...';

    try {
        const result = await ipcRenderer.invoke('open-admin-panel');
        if (result.success) {
            statusElement.textContent = result.message;
        } else {
            statusElement.textContent = result.error;
        }
    } catch (error) {
        console.error('打开管理面板失败:', error);
        statusElement.textContent = '打开管理面板失败: ' + error.message;
    }
}

// 打开设置页面
async function openSettings() {
    const statusElement = document.getElementById('status');
    statusElement.textContent = getNestedValue(i18nTexts, 'main.status.opening_settings') || '正在打开设置页面...';

    try {
        const result = await ipcRenderer.invoke('open-settings');
        if (result.success) {
            statusElement.textContent = getNestedValue(i18nTexts, 'main.status.settings_opened') || '设置页面已打开';
        } else {
            statusElement.textContent = result.error;
        }
    } catch (error) {
        console.error('打开设置页面失败:', error);
        statusElement.textContent = '打开设置页面失败: ' + error.message;
    }
}





// 打开开发者工具
function openDevTools() {
    // 这个功能需要在主进程中实现
    const statusElement = document.getElementById('status');
    statusElement.textContent = '开发者工具已在主进程中配置';
}

// 监听键盘快捷键
document.addEventListener('keydown', (event) => {
    // Ctrl+R 或 Cmd+R 刷新页面
    if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
        event.preventDefault();
        location.reload();
    }

    // F12 打开开发者工具（在开发模式下）
    if (event.key === 'F12') {
        event.preventDefault();
        openDevTools();
    }
});

// 添加一些交互效果
document.querySelectorAll('button').forEach(button => {
    button.addEventListener('mouseenter', () => {
        button.style.transform = 'translateY(-2px)';
    });

    button.addEventListener('mouseleave', () => {
        button.style.transform = 'translateY(0)';
    });
});
