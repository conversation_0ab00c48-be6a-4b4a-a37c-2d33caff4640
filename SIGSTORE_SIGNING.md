# Sigstore 代码签名集成

本项目已集成 Sigstore 免费代码签名，确保发布的应用程序的完整性和真实性。

## 什么是 Sigstore？

Sigstore 是一个免费的代码签名服务，提供：
- 🆓 **免费使用** - 无需购买昂贵的代码签名证书
- 🔒 **安全可靠** - 基于 OpenID Connect 身份验证
- 🌐 **透明日志** - 所有签名都记录在公开的透明日志中
- ✅ **广泛支持** - 被主要操作系统和安全工具认可

## 签名流程

### 1. 自动签名（GitHub Actions）

当推送带有 `v*` 标签的提交时，GitHub Actions 会自动：

1. 构建各平台的应用程序
2. 使用 Sigstore 对所有可执行文件进行签名
3. 生成签名文件（`.sig`）和证书文件（`.crt`）
4. 将签名文件与应用程序一起发布

### 2. 签名验证

#### 自动验证（应用内）
- 应用程序在安装更新前会自动验证签名
- 如果签名验证失败，会阻止安装（生产模式）
- 开发模式下会显示警告但允许安装

#### 手动验证
```bash
# 验证单个文件
npm run verify-signature path/to/file.exe

# 验证 dist 目录中的所有文件
npm run verify-dist

# 或直接使用脚本
node scripts/verify-signature.js --dir dist
```

## 安装 Cosign（用于本地验证）

### Windows
```powershell
# 使用 Chocolatey
choco install cosign

# 或手动下载
curl -O -L "https://github.com/sigstore/cosign/releases/latest/download/cosign-windows-amd64.exe"
```

### macOS
```bash
# 使用 Homebrew
brew install cosign

# 使用 MacPorts
sudo port install cosign
```

### Linux
```bash
# Ubuntu/Debian
curl -O -L "https://github.com/sigstore/cosign/releases/latest/download/cosign-linux-amd64"
sudo mv cosign-linux-amd64 /usr/local/bin/cosign
sudo chmod +x /usr/local/bin/cosign

# 或使用包管理器（如果可用）
# Arch Linux
sudo pacman -S cosign
```

## 验证发布文件

### 从 GitHub Releases 下载并验证

1. 从 [Releases 页面](https://github.com/laixiao/AiTools/releases) 下载应用程序
2. 同时下载对应的 `.sig` 和 `.crt` 文件
3. 使用 cosign 验证：

```bash
# 验证 Windows 版本
cosign verify-blob \
  --signature ai-tools-1.0.36.exe.sig \
  --certificate ai-tools-1.0.36.exe.crt \
  --certificate-identity-regexp ".*" \
  --certificate-oidc-issuer-regexp ".*" \
  ai-tools-1.0.36.exe

# 验证 macOS 版本
cosign verify-blob \
  --signature ai-tools-1.0.36.dmg.sig \
  --certificate ai-tools-1.0.36.dmg.crt \
  --certificate-identity-regexp ".*" \
  --certificate-oidc-issuer-regexp ".*" \
  ai-tools-1.0.36.dmg
```

### 验证成功示例
```
Verified OK
```

### 验证失败示例
```
Error: signature verification failed
```

## 技术细节

### 签名过程
1. **身份验证**: GitHub Actions 使用 OIDC 令牌进行身份验证
2. **签名生成**: Cosign 使用短期密钥对文件进行签名
3. **证书颁发**: Fulcio CA 颁发短期证书
4. **透明日志**: 签名记录在 Rekor 透明日志中

### 文件结构
```
dist/
├── ai-tools-1.0.36.exe          # 应用程序
├── ai-tools-1.0.36.exe.sig      # 签名文件
├── ai-tools-1.0.36.exe.crt      # 证书文件
├── ai-tools-1.0.36.dmg          # macOS 应用
├── ai-tools-1.0.36.dmg.sig      # macOS 签名
└── ai-tools-1.0.36.dmg.crt      # macOS 证书
```

### 集成的验证功能
- **更新验证**: 自动更新时验证新版本的签名
- **本地验证**: 提供脚本验证本地文件
- **批量验证**: 支持验证整个目录的文件

## 安全优势

### 对比传统代码签名
| 特性 | 传统代码签名 | Sigstore |
|------|-------------|----------|
| 成本 | 昂贵（数百美元/年） | 免费 |
| 证书管理 | 需要安全存储私钥 | 无需管理长期密钥 |
| 透明度 | 不透明 | 公开透明日志 |
| 自动化 | 复杂 | 简单集成 |
| 安全性 | 依赖证书安全 | 基于身份验证 |

### 用户体验改善
- ✅ 减少安全警告
- ✅ 提高用户信任度
- ✅ 支持自动更新
- ✅ 符合企业安全策略

## 故障排除

### 常见问题

**Q: 验证时提示 "cosign not found"**
A: 请安装 cosign 工具，参考上面的安装说明。

**Q: 验证失败但文件是从官方下载的**
A: 检查是否下载了对应的 `.sig` 和 `.crt` 文件，确保文件名匹配。

**Q: 自动更新被阻止**
A: 检查网络连接，确保能够下载签名文件。如果问题持续，可以手动下载并验证。

**Q: 开发环境中如何跳过验证？**
A: 使用 `--dev` 参数启动应用，或设置 `NODE_ENV=development`。

### 调试信息
应用程序会在控制台输出详细的验证日志：
```
🔍 开始验证更新文件签名...
下载签名文件: https://github.com/laixiao/AiTools/releases/download/v1.0.36/ai-tools-1.0.36.exe.sig
签名文件下载完成: ai-tools-1.0.36.exe.sig
验证文件签名: ai-tools-1.0.36.exe
✅ 签名验证成功: ai-tools-1.0.36.exe
🎉 更新文件签名验证成功！
```

## 相关链接

- [Sigstore 官网](https://www.sigstore.dev/)
- [Cosign 文档](https://docs.sigstore.dev/cosign/overview/)
- [GitHub OIDC 文档](https://docs.github.com/en/actions/deployment/security-hardening-your-deployments/about-security-hardening-with-openid-connect)
- [项目 Releases](https://github.com/laixiao/AiTools/releases)
