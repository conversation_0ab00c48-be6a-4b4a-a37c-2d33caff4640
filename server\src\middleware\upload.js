/**
 * 文件上传中间件
 * 使用 multer 处理文件上传
 */

const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const sharp = require('sharp');

/**
 * 配置常量
 */
const UPLOAD_CONFIG = {
  // 上传目录
  UPLOAD_DIR: path.join(__dirname, '../../uploads'),
  TEMP_DIR: path.join(__dirname, '../../uploads/temp'),

  // 文件大小限制 (1GB)
  MAX_FILE_SIZE: 1024 * 1024 * 1024,
  
  // 允许的图片类型
  ALLOWED_IMAGE_TYPES: [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml'
  ],
  
  // 允许的文档类型
  ALLOWED_DOCUMENT_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
  ],
  
  // 图片压缩配置
  IMAGE_COMPRESSION: {
    quality: 85,
    maxWidth: 1920,
    maxHeight: 1080
  }
};

/**
 * 确保上传目录存在
 */
function ensureUploadDirs() {
  const dirs = [UPLOAD_CONFIG.UPLOAD_DIR, UPLOAD_CONFIG.TEMP_DIR];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`📁 创建上传目录: ${dir}`);
    }
  });
}

// 初始化目录
ensureUploadDirs();

/**
 * 生成唯一文件名
 */
function generateFileName(originalname) {
  const ext = path.extname(originalname).toLowerCase();
  const name = path.basename(originalname, ext);
  const timestamp = Date.now();
  const uuid = uuidv4().split('-')[0]; // 使用UUID的前8位
  
  return `${timestamp}-${uuid}${ext}`;
}

/**
 * 文件类型验证
 */
function fileFilter(req, file, cb) {
  const allowedTypes = [
    ...UPLOAD_CONFIG.ALLOWED_IMAGE_TYPES,
    ...UPLOAD_CONFIG.ALLOWED_DOCUMENT_TYPES
  ];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`不支持的文件类型: ${file.mimetype}`), false);
  }
}

/**
 * 图片文件过滤器
 */
function imageFilter(req, file, cb) {
  if (UPLOAD_CONFIG.ALLOWED_IMAGE_TYPES.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`不支持的图片类型: ${file.mimetype}`), false);
  }
}

/**
 * Multer 存储配置
 */
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, UPLOAD_CONFIG.UPLOAD_DIR);
  },
  filename: function (req, file, cb) {
    const fileName = generateFileName(file.originalname);
    cb(null, fileName);
  }
});

/**
 * 临时存储配置（用于需要处理的文件）
 */
const tempStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, UPLOAD_CONFIG.TEMP_DIR);
  },
  filename: function (req, file, cb) {
    const fileName = generateFileName(file.originalname);
    cb(null, fileName);
  }
});

/**
 * 基础上传中间件
 */
const upload = multer({
  storage: storage,
  limits: {
    fileSize: UPLOAD_CONFIG.MAX_FILE_SIZE,
    files: 5 // 最多5个文件
  },
  fileFilter: fileFilter
});

/**
 * 图片上传中间件
 */
const uploadImage = multer({
  storage: tempStorage, // 先存到临时目录
  limits: {
    fileSize: UPLOAD_CONFIG.MAX_FILE_SIZE,
    files: 1
  },
  fileFilter: imageFilter
});

/**
 * 图片处理中间件
 */
async function processImage(req, res, next) {
  if (!req.file) {
    return next();
  }
  
  try {
    const tempPath = req.file.path;
    const finalFileName = generateFileName(req.file.originalname);
    const finalPath = path.join(UPLOAD_CONFIG.UPLOAD_DIR, finalFileName);
    
    // 获取图片信息
    const metadata = await sharp(tempPath).metadata();
    
    // 处理图片
    let sharpInstance = sharp(tempPath);
    
    // 如果图片过大，进行压缩
    if (metadata.width > UPLOAD_CONFIG.IMAGE_COMPRESSION.maxWidth || 
        metadata.height > UPLOAD_CONFIG.IMAGE_COMPRESSION.maxHeight) {
      sharpInstance = sharpInstance.resize(
        UPLOAD_CONFIG.IMAGE_COMPRESSION.maxWidth,
        UPLOAD_CONFIG.IMAGE_COMPRESSION.maxHeight,
        { 
          fit: 'inside',
          withoutEnlargement: true
        }
      );
    }
    
    // 压缩质量
    if (req.file.mimetype === 'image/jpeg') {
      sharpInstance = sharpInstance.jpeg({ 
        quality: UPLOAD_CONFIG.IMAGE_COMPRESSION.quality 
      });
    } else if (req.file.mimetype === 'image/png') {
      sharpInstance = sharpInstance.png({ 
        quality: UPLOAD_CONFIG.IMAGE_COMPRESSION.quality 
      });
    }
    
    // 保存处理后的图片
    await sharpInstance.toFile(finalPath);
    
    // 获取处理后的文件信息
    const stats = fs.statSync(finalPath);
    const processedMetadata = await sharp(finalPath).metadata();
    
    // 更新文件信息
    req.file.filename = finalFileName;
    req.file.path = finalPath;
    req.file.size = stats.size;
    req.file.processedMetadata = {
      width: processedMetadata.width,
      height: processedMetadata.height,
      format: processedMetadata.format,
      channels: processedMetadata.channels
    };
    
    // 删除临时文件
    fs.unlinkSync(tempPath);
    
    next();
  } catch (error) {
    console.error('图片处理失败:', error);
    
    // 清理临时文件
    if (req.file && req.file.path && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    return res.status(400).json({
      status: 'error',
      message: '图片处理失败',
      error: error.message
    });
  }
}

/**
 * 错误处理中间件
 */
function handleUploadError(error, req, res, next) {
  console.error('文件上传错误:', error);
  
  // 清理已上传的文件
  if (req.file && req.file.path && fs.existsSync(req.file.path)) {
    fs.unlinkSync(req.file.path);
  }
  
  if (req.files) {
    req.files.forEach(file => {
      if (file.path && fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }
    });
  }
  
  let message = '文件上传失败';
  let statusCode = 400;
  
  if (error instanceof multer.MulterError) {
    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        message = `文件大小超过限制 (${UPLOAD_CONFIG.MAX_FILE_SIZE / 1024 / 1024}MB)`;
        break;
      case 'LIMIT_FILE_COUNT':
        message = '文件数量超过限制';
        break;
      case 'LIMIT_UNEXPECTED_FILE':
        message = '意外的文件字段';
        break;
      default:
        message = error.message;
    }
  } else if (error.message) {
    message = error.message;
  }
  
  res.status(statusCode).json({
    status: 'error',
    message: message,
    error: error.code || 'UPLOAD_ERROR'
  });
}

/**
 * 删除文件工具函数
 */
function deleteFile(filePath) {
  return new Promise((resolve, reject) => {
    if (!filePath || !fs.existsSync(filePath)) {
      return resolve();
    }
    
    fs.unlink(filePath, (err) => {
      if (err) {
        console.error('删除文件失败:', err);
        reject(err);
      } else {
        console.log('文件删除成功:', filePath);
        resolve();
      }
    });
  });
}

/**
 * 获取文件信息工具函数
 */
function getFileInfo(filePath) {
  if (!fs.existsSync(filePath)) {
    return null;
  }
  
  const stats = fs.statSync(filePath);
  return {
    size: stats.size,
    created: stats.birthtime,
    modified: stats.mtime,
    isFile: stats.isFile(),
    isDirectory: stats.isDirectory()
  };
}

module.exports = {
  upload,
  uploadImage,
  processImage,
  handleUploadError,
  deleteFile,
  getFileInfo,
  UPLOAD_CONFIG
};
