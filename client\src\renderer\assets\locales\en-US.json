{"app": {"title": "AI Tools", "welcome": "Welcome to this cross-platform Electron application!", "version": "Version", "loading": "Loading...", "ready": "Ready"}, "common": {"back": "Back", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "edit": "Edit", "add": "Add", "close": "Close", "ok": "OK", "yes": "Yes", "no": "No", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information"}, "login": {"title": "AI Tools - Authorization", "auth_code": "Authorization Code", "placeholder": "Please enter your authorization code", "verify": "Verify Authorization Code", "verifying": "Verifying authorization code...", "help_text": "Please enter a valid authorization code to access the AI Tools application.", "demo_codes": "Demo Authorization Codes:", "admin_code": "Admin: ADMIN2024", "user_code": "User: RDKKQF76AVX6WP4E", "error": {"empty": "Please enter authorization code", "invalid": "Invalid authorization code", "network": "Network connection failed, please check if the backend service is running"}}, "main": {"welcome": "Welcome, {username}!", "buttons": {"admin_panel": "Admin Panel", "settings": "Settings", "check_updates": "Check Updates", "about": "About", "logout": "Logout", "dev_tools": "Developer Tools"}, "features": {"title": "Features", "cross_platform": "Cross-platform support (Windows, macOS, Linux)", "auto_update": "Auto-update functionality", "github_actions": "GitHub Actions automated builds", "modern_ui": "Modern user interface", "dev_friendly": "Developer-friendly configuration", "admin_features": "Admin features (Brand management, User management)"}, "status": {"checking_updates": "Checking for updates...", "dev_mode_no_update": "Cannot check for updates in development mode", "update_failed": "Failed to check for updates", "opening_admin": "Opening admin panel...", "opening_settings": "Opening settings page...", "settings_opened": "Settings page opened", "logging_out": "Logging out...", "logged_out": "Logged out successfully", "dev_tools_configured": "Developer tools configured in main process"}, "logout": {"confirm": "Are you sure you want to logout?"}, "about": {"title": "About this application", "description": "This is a cross-platform desktop application built with Electron.", "platforms": "Supports Windows, macOS, and Linux systems.", "features": "Features auto-update functionality to automatically get the latest version."}, "user_info": {"title": "User Information", "username": "Username: {username}", "type": "Type: {userType}", "status": "Status: {status}", "active": "Active", "inactive": "Inactive"}}, "settings": {"title": "Settings", "language": {"title": "Language Settings", "interface": "Interface Language", "description": "Select the display language for the application", "chinese": "中文 (简体)", "english": "English", "preview": "Language Preview: The selected language will be applied to the interface immediately"}, "app": {"title": "Application Settings", "startup": "Auto Start", "startup_description": "Automatically run the application when the system starts", "minimize_to_tray": "Minimize to System Tray", "minimize_description": "Minimize to system tray instead of exiting when closing the window", "auto_update": "Auto Update", "update_description": "Automatically check and download application updates"}, "update": {"title": "Update Settings", "channel": "Update Channel", "channel_description": "Select the download source for application updates", "github": "<PERSON><PERSON><PERSON><PERSON> (Default)", "gitee": "<PERSON><PERSON><PERSON> (China Mirror)", "custom": "Custom", "custom_url": "Custom Update URL", "custom_url_description": "Enter a custom update check URL", "check_now": "Check for Updates", "check_description": "Check immediately for available application updates", "check_button": "Check for Updates", "checking": "Checking...", "checking_message": "Checking for updates...", "available": "New version available", "latest": "You are using the latest version", "failed": "Failed to check for updates", "error": "Error occurred while checking for updates", "card": {"new_version_found": "New version {version} found", "downloading": "Downloading new version...", "download_complete": "Download complete, install now?", "download_failed": "Update failed: {error}", "network_error": "Network connection error", "preparing": "Preparing download...", "installing": "Restarting to install...", "progress": "{percent}% • {speed}/s • {transferred} / {total}", "buttons": {"update_now": "Update Now", "install_now": "Install Now", "later": "Later", "skip_version": "Skip This Version", "close": "Close", "retry": "Retry"}}}, "appearance": {"title": "Appearance Settings", "theme": "Theme", "theme_description": "Select the appearance theme for the application", "auto": "Follow System", "light": "Light", "dark": "Dark"}, "about": {"title": "About", "version": "Application Version", "version_description": "Current application version information", "build_date": "Build Date: {date}"}, "actions": {"reset": "Reset Settings", "save": "Save Settings"}, "user": {"title": "User Information", "current_user": "Current User"}, "messages": {"saved": "Setting<PERSON> saved", "reset": "Settings reset to default values", "language_changed": "Language changed, restart the application to take effect", "theme_changed": "Theme changed", "reset_confirm": "Are you sure you want to reset all settings to default values?", "save_failed": "Failed to save settings", "auto_start_updated": "Auto start setting has been updated", "tray_updated": "System tray setting has been updated"}}, "admin": {"title": "Admin Panel", "brand_management": "Brand Management", "user_management": "User Management", "file_management": "File Management", "brand_list": "Brand List", "user_list": "User List", "file_list": "File List", "add_brand": "Add Brand", "add_user": "Add User", "table": {"icon": "Icon", "brand_name": "Brand Name", "description": "Description", "status": "Status", "actions": "Actions", "username": "Username", "email": "Email", "display_name": "Display Name", "type": "Type", "auth_code": "Auth Code", "login_count": "Login <PERSON>", "created_at": "Created At", "preview": "Preview", "file_name": "File Name", "file_type": "Type", "file_size": "Size", "uploader": "Uploader", "upload_time": "Upload Time"}, "file": {"stats": {"total_files": "Total Files", "total_size": "Total Size", "image_files": "Image Files", "other_files": "Other Files"}, "filters": {"all_types": "All Types", "image": "Image", "document": "Document", "video": "Video", "audio": "Audio", "other": "Other", "search_placeholder": "Search file name...", "sort": {"newest": "Newest", "oldest": "Oldest", "largest": "Largest", "smallest": "Smallest", "name": "Name"}}, "actions": {"refresh": "Refresh", "cleanup": "Cleanup", "view": "View", "download": "Download", "delete": "Delete"}, "messages": {"no_files": "No files", "upload_success": "File uploaded successfully", "upload_failed": "File upload failed", "delete_confirm": "Are you sure you want to delete file \"{fileName}\"? This action cannot be undone!", "delete_success": "File deleted successfully", "delete_failed": "File deletion failed", "cleanup_confirm": "Are you sure you want to cleanup invalid files? This will delete all files without associated records.", "cleanup_developing": "Cleanup feature is under development..."}}, "loading": "Loading...", "no_permission": "Only administrators can access the admin panel", "login_required": "Please login first"}, "errors": {"network": "Network connection failed", "server": "Server error", "unauthorized": "Unauthorized access", "not_found": "Resource not found", "unknown": "Unknown error"}, "messages": {"operation_success": "Operation successful", "operation_failed": "Operation failed", "data_saved": "Data saved", "data_deleted": "Data deleted", "confirm_delete": "Are you sure you want to delete? This action cannot be undone."}}