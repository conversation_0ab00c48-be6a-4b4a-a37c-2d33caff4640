/**
 * 应用配置模块
 * 统一管理应用的各种配置
 */

require('dotenv').config();

/**
 * 应用基础配置
 */
const APP_CONFIG = {
  // 服务器配置
  server: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || 'localhost',
    env: process.env.NODE_ENV || 'development'
  },
  
  // CORS 配置
  cors: {
    origin: process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://127.0.0.1:3000',
      'http://127.0.0.1:3001'
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  },
  
  // 请求限制配置
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 分钟
    max: process.env.RATE_LIMIT_MAX || 100, // 限制每个IP 15分钟内最多100个请求
    message: {
      error: '请求过于频繁',
      message: '请稍后再试'
    }
  },
  
  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'combined',
    file: {
      enabled: process.env.LOG_FILE_ENABLED === 'true',
      path: process.env.LOG_FILE_PATH || './logs/app.log',
      maxSize: process.env.LOG_FILE_MAX_SIZE || '10m',
      maxFiles: process.env.LOG_FILE_MAX_FILES || '5'
    }
  },
  
  // 安全配置
  security: {
    helmet: {
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"]
        }
      }
    },
    
    // JWT 配置（如果需要）
    jwt: {
      secret: process.env.JWT_SECRET || 'your-secret-key',
      expiresIn: process.env.JWT_EXPIRES_IN || '24h'
    }
  },
  
  // API 配置
  api: {
    prefix: '/api',
    version: 'v1',
    documentation: {
      enabled: true,
      path: '/api-docs'
    }
  },
  
  // 文件上传配置
  upload: {
    maxFileSize: process.env.UPLOAD_MAX_FILE_SIZE || '1gb',
    allowedTypes: process.env.UPLOAD_ALLOWED_TYPES ?
      process.env.UPLOAD_ALLOWED_TYPES.split(',') :
      ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
    destination: process.env.UPLOAD_DESTINATION || './uploads'
  }
};

/**
 * 环境特定配置
 */
const ENVIRONMENT_CONFIG = {
  development: {
    debug: true,
    logging: {
      level: 'debug',
      console: true
    },
    cors: {
      origin: true // 开发环境允许所有来源
    }
  },

  production: {
    debug: false,
    logging: {
      level: 'warn',
      console: false,
      file: {
        enabled: true
      }
    },
    security: {
      helmet: {
        contentSecurityPolicy: true,
        hsts: true
      }
    }
  }
};

/**
 * 获取当前环境
 */
function getEnvironment() {
  return process.env.NODE_ENV || 'development';
}

/**
 * 获取合并后的配置
 */
function getConfig() {
  const env = getEnvironment();
  const envConfig = ENVIRONMENT_CONFIG[env] || {};
  
  // 深度合并配置
  return mergeDeep(APP_CONFIG, envConfig);
}

/**
 * 深度合并对象
 */
function mergeDeep(target, source) {
  const result = { ...target };
  
  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      result[key] = mergeDeep(result[key] || {}, source[key]);
    } else {
      result[key] = source[key];
    }
  }
  
  return result;
}

/**
 * 验证必需的环境变量
 */
function validateEnvironment() {
  const required = [];
  
  // 生产环境必需的环境变量
  if (getEnvironment() === 'production') {
    required.push(
      'MONGODB_URI',
      'JWT_SECRET'
    );
  }
  
  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`缺少必需的环境变量: ${missing.join(', ')}`);
  }
}

/**
 * 获取服务器信息
 */
function getServerInfo() {
  const config = getConfig();
  return {
    name: config.name,
    version: config.version,
    description: config.description,
    environment: getEnvironment(),
    port: config.server.port,
    host: config.server.host,
    apiPrefix: config.api.prefix,
    documentation: config.api.documentation.enabled ? 
      `http://${config.server.host}:${config.server.port}${config.api.documentation.path}` : 
      null
  };
}

/**
 * 检查配置是否有效
 */
function validateConfig() {
  try {
    validateEnvironment();
    
    const config = getConfig();
    
    // 验证端口号
    if (isNaN(config.server.port) || config.server.port < 1 || config.server.port > 65535) {
      throw new Error('无效的端口号');
    }
    
    console.log('✅ 配置验证通过');
    return true;
  } catch (error) {
    console.error('❌ 配置验证失败:', error.message);
    throw error;
  }
}

/**
 * 打印配置信息
 */
function printConfig() {
  const config = getConfig();
  const serverInfo = getServerInfo();
  
  console.log('\n📋 应用配置信息:');
  console.log(`   名称: ${serverInfo.name}`);
  console.log(`   版本: ${serverInfo.version}`);
  console.log(`   环境: ${serverInfo.environment}`);
  console.log(`   地址: http://${serverInfo.host}:${serverInfo.port}`);
  console.log(`   API: ${serverInfo.apiPrefix}`);
  if (serverInfo.documentation) {
    console.log(`   文档: ${serverInfo.documentation}`);
  }
  console.log('');
}

module.exports = {
  getConfig,
  getEnvironment,
  getServerInfo,
  validateConfig,
  printConfig,
  APP_CONFIG,
  ENVIRONMENT_CONFIG
};
