/**
 * 更新配置模块
 * 统一管理更新相关的配置参数
 */

/**
 * 更新重试配置
 */
const RETRY_CONFIG = {
  // 最大重试次数 - 增加到5次
  MAX_RETRIES: 5,

  // 基础延迟时间（毫秒）- 增加到3秒
  BASE_DELAY: 3000,

  // 最大延迟时间（毫秒）- 增加到30秒
  MAX_DELAY: 30000,

  // 网络检查超时时间（毫秒）- 增加到10秒
  NETWORK_CHECK_TIMEOUT: 10000,

  // 指数退避因子
  BACKOFF_FACTOR: 2
};

/**
 * 网络错误类型列表
 */
const NETWORK_ERROR_TYPES = [
  'ERR_CONNECTION_CLOSED',
  'ERR_CONNECTION_RESET',
  'ERR_CONNECTION_REFUSED',
  'ERR_NETWORK_CHANGED',
  'ERR_INTERNET_DISCONNECTED',
  'ENOTFOUND',
  'ECONNRESET',
  'ETIMEDOUT',
  'ECONNABORTED',
  'EHOSTUNREACH',
  'ENE<PERSON>NREACH',
  'ENOTCONN',
  'socket hang up',
  'timeout',
  'TIMEOUT',
  'Request timeout',
  'Download timeout',
  'ERR_SOCKET_TIMEOUT',
  'ERR_STREAM_PREMATURE_CLOSE'
];

/**
 * 更新源配置
 */
const UPDATE_SOURCES = {
  GITHUB: {
    name: 'GitHub',
    provider: 'github',
    owner: 'laixiao',
    repo: 'AiTools',
    url: 'https://github.com/laixiao/AiTools/releases'
  },
  
  GITEE: {
    name: 'Gitee',
    provider: 'generic',
    url: 'https://gitee.com/laixiao/AiTools/releases'
  },
  
  CUSTOM: {
    name: 'Custom',
    provider: 'generic',
    url: null // 由用户配置
  }
};

/**
 * 更新状态枚举
 */
const UPDATE_STATES = {
  IDLE: 'idle',
  CHECKING: 'checking',
  AVAILABLE: 'available',
  NOT_AVAILABLE: 'not_available',
  DOWNLOADING: 'downloading',
  DOWNLOADED: 'downloaded',
  ERROR: 'error',
  RETRYING: 'retrying',
  RETRY_FAILED: 'retry_failed'
};

/**
 * 计算重试延迟时间
 * @param {number} retryCount - 当前重试次数（从1开始）
 * @returns {number} 延迟时间（毫秒）
 */
function calculateRetryDelay(retryCount) {
  const delay = RETRY_CONFIG.BASE_DELAY * Math.pow(RETRY_CONFIG.BACKOFF_FACTOR, retryCount - 1);
  return Math.min(delay, RETRY_CONFIG.MAX_DELAY);
}

/**
 * 判断是否为网络错误
 * @param {Error} error - 错误对象
 * @returns {boolean} 是否为网络错误
 */
function isNetworkError(error) {
  if (!error) return false;
  
  const errorMessage = error.message || String(error);
  const errorCode = error.code || '';
  
  return NETWORK_ERROR_TYPES.some(netError => 
    errorMessage.includes(netError) || errorCode === netError
  );
}

/**
 * 获取错误类型描述
 * @param {Error} error - 错误对象
 * @returns {object} 错误描述信息
 */
function getErrorInfo(error) {
  const isNetwork = isNetworkError(error);
  const message = error.message || String(error);
  
  return {
    isNetworkError: isNetwork,
    message: message,
    code: error.code || null,
    canRetry: isNetwork,
    userFriendlyMessage: isNetwork 
      ? '网络连接出现问题，请检查网络设置'
      : '更新过程中出现错误'
  };
}

/**
 * 创建更新源配置
 * @param {string} type - 更新源类型 ('github', 'gitee', 'custom')
 * @param {string} customUrl - 自定义URL（仅当type为'custom'时使用）
 * @returns {object} 更新源配置
 */
function createUpdateSourceConfig(type, customUrl = null) {
  switch (type.toLowerCase()) {
    case 'github':
      return {
        provider: UPDATE_SOURCES.GITHUB.provider,
        owner: UPDATE_SOURCES.GITHUB.owner,
        repo: UPDATE_SOURCES.GITHUB.repo
      };
      
    case 'gitee':
    case 'custom':
      return {
        provider: 'generic',
        url: type === 'custom' ? customUrl : UPDATE_SOURCES.GITEE.url
      };
      
    default:
      console.warn(`未知的更新源类型: ${type}，使用默认GitHub源`);
      return createUpdateSourceConfig('github');
  }
}

/**
 * 验证更新配置
 * @param {object} config - 更新配置
 * @returns {object} 验证结果
 */
function validateUpdateConfig(config) {
  const errors = [];
  
  if (!config) {
    errors.push('配置对象不能为空');
    return { valid: false, errors };
  }
  
  if (!config.provider) {
    errors.push('必须指定更新源提供者');
  }
  
  if (config.provider === 'github') {
    if (!config.owner || !config.repo) {
      errors.push('GitHub源必须指定owner和repo');
    }
  } else if (config.provider === 'generic') {
    if (!config.url) {
      errors.push('通用源必须指定URL');
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 获取默认更新配置
 * @returns {object} 默认配置
 */
function getDefaultUpdateConfig() {
  return {
    source: 'github',
    autoUpdate: true,
    autoDownload: true,
    checkOnStartup: true,
    retryConfig: { ...RETRY_CONFIG },
    skippedVersions: []
  };
}

module.exports = {
  // 配置常量
  RETRY_CONFIG,
  NETWORK_ERROR_TYPES,
  UPDATE_SOURCES,
  UPDATE_STATES,
  
  // 工具函数
  calculateRetryDelay,
  isNetworkError,
  getErrorInfo,
  createUpdateSourceConfig,
  validateUpdateConfig,
  getDefaultUpdateConfig
};
