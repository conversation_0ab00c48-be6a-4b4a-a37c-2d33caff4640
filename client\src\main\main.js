const { app, BrowserWindow, Menu, dialog, ipcMain, shell, net, Tray } = require('electron');
const { autoUpdater } = require('electron-updater');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');
const i18n = require('./i18n');
const { APP_CONFIG, getWindowConfig, printEnvConfig, isDevelopment } = require('../shared/config/env');
const {
  RETRY_CONFIG,
  isNetworkError,
  calculateRetryDelay,
  getErrorInfo,
  createUpdateSourceConfig
} = require('../shared/config/update');
const {
  configureUpdaterAdvanced,
  downloadWithRetry,
  isRetryableError,
  getDownloadState,
  resetDownloadState,
  cleanupDownload
} = require('./updater-config');
const {
  configureSignatureVerification,
  validateUpdate,
  isCosignAvailable
} = require('./signature-verifier');
const isDev = isDevelopment() || process.argv.includes('--dev');

// 保持对窗口对象的全局引用，如果不这么做的话，当JavaScript对象被
// 垃圾回收的时候，窗口会被自动地关闭
let mainWindow;
let currentUser = null; // 当前登录用户
let appSettings = {}; // 应用设置
let tray = null; // 系统托盘

// 设置全局mainWindow引用，供updater-config使用
global.mainWindow = null;

// 更新状态（用于在渲染进程显示提示/进度）
let updateState = {
  available: false,
  info: null,
  downloading: false,
  progress: null,
  downloaded: false,
  retrying: false,
  retryCount: 0,
  maxRetries: RETRY_CONFIG.MAX_RETRIES,
  lastError: null
};

// 网络连接检查
async function checkNetworkConnection() {
  return new Promise((resolve) => {
    const request = net.request('https://www.github.com');
    request.on('response', () => {
      resolve(true);
    });
    request.on('error', () => {
      resolve(false);
    });
    request.setTimeout(RETRY_CONFIG.NETWORK_CHECK_TIMEOUT, () => {
      resolve(false);
    });
    request.end();
  });
}

// 延迟函数
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 重试下载更新
async function retryUpdateDownload() {
  if (updateState.retryCount >= updateState.maxRetries) {
    console.log('已达到最大重试次数，停止重试');
    updateState.retrying = false;
    if (mainWindow) {
      mainWindow.webContents.send('update-retry-failed', {
        error: '重试次数已达上限',
        retryCount: updateState.retryCount
      });
    }
    return;
  }

  updateState.retryCount++;
  updateState.retrying = true;

  console.log(`开始第 ${updateState.retryCount} 次重试下载更新...`);

  if (mainWindow) {
    mainWindow.webContents.send('update-retrying', {
      retryCount: updateState.retryCount,
      maxRetries: updateState.maxRetries
    });
  }

  // 检查网络连接
  const isConnected = await checkNetworkConnection();
  if (!isConnected) {
    console.log('网络连接不可用，等待后重试...');
    await delay(5000); // 等待5秒
    return retryUpdateDownload(); // 递归重试
  }

  // 指数退避延迟
  const delayTime = calculateRetryDelay(updateState.retryCount);
  await delay(delayTime);

  try {
    // 重新配置autoUpdater以确保设置生效
    const channel = (appSettings && appSettings.updateChannel) || 'github';
    const customUrl = (appSettings && appSettings.customUpdateUrl) || '';

    // 应用高级配置
    configureUpdaterAdvanced();

    if (channel === 'custom' && customUrl) {
      autoUpdater.setFeedURL({
        provider: 'generic',
        url: customUrl
      });
    } else {
      autoUpdater.setFeedURL({
        provider: 'github',
        owner: 'laixiao',
        repo: 'AiTools'
      });
    }

    console.log('开始重新下载更新...');
    await autoUpdater.downloadUpdate();
  } catch (error) {
    console.error(`第 ${updateState.retryCount} 次重试失败:`, error);
    updateState.lastError = error;

    // 使用新的错误检查函数
    const canRetry = isRetryableError(error) || isNetworkError(error);

    if (canRetry) {
      // 如果仍是可重试错误，继续重试
      console.log('检测到可重试错误，将继续重试:', error.message);
      return retryUpdateDownload();
    } else {
      // 如果是其他错误，停止重试
      updateState.retrying = false;
      if (mainWindow) {
        mainWindow.webContents.send('update-retry-failed', {
          error: error.message,
          retryCount: updateState.retryCount
        });
      }
    }
  }
}

// 配置自动更新
function configureAutoUpdater() {
  // 设置更新服务器（根据设置的更新通道）
  if (!isDev) {
    try {
      const channel = (appSettings && appSettings.updateChannel) || 'github';
      const customUrl = (appSettings && appSettings.customUpdateUrl) || '';

      // 应用高级配置
      configureUpdaterAdvanced();

      // 配置签名验证
      configureSignatureVerification(autoUpdater);

      if (channel === 'custom' && customUrl) {
        console.log('使用自定义更新源:', customUrl);
        autoUpdater.setFeedURL({
          provider: 'generic',
          url: customUrl
        });
      } else {
        console.log('使用 GitHub 作为更新源');
        autoUpdater.setFeedURL({
          provider: 'github',
          owner: 'laixiao',
          repo: 'AiTools'
        });
      }

      // 根据设置决定是否自动下载，但始终检查更新
      autoUpdater.autoDownload = appSettings.autoUpdate !== false;
      autoUpdater.checkForUpdates().catch((e) => console.error('检查更新失败:', e));
    } catch (e) {
      console.error('配置自动更新失败:', e);
    }
  }

  // 监听更新事件
  autoUpdater.on('checking-for-update', () => {
    console.log('正在检查更新...');
  });

  autoUpdater.on('update-available', (info) => {
    console.log('发现新版本:', info.version);

    // 检查是否跳过此版本
    const skippedVersions = (appSettings && appSettings.skippedVersions) || [];
    if (skippedVersions.includes(info.version)) {
      console.log('跳过版本:', info.version);
      return;
    }

    updateState.available = true;
    updateState.info = info;
    if (mainWindow) {
      mainWindow.webContents.send('update-available', info);
    }

    // 新的更新流程：
    // 如果开启了自动更新，不立即弹窗，而是静默下载，在下载完成后提示安装
    // 如果关闭了自动更新，在页面显示新版本标签，由用户决定何时下载
    if (appSettings.autoUpdate !== false) {
      console.log('自动更新已开启，开始后台下载...');
    } else {
      console.log('自动更新已关闭，等待用户手动触发下载');
    }
  });

  autoUpdater.on('update-not-available', (info) => {
    console.log('当前已是最新版本');
    updateState.available = false;
    updateState.info = null;
    if (mainWindow) {
      mainWindow.webContents.send('update-not-available', info);
    }
  });

  autoUpdater.on('error', async (err) => {
    console.error('更新出错:', err);
    updateState.lastError = err;

    // 如果正在重试中，不要重复处理
    if (updateState.retrying) {
      return;
    }

    // 检查是否为网络错误、超时错误或下载中断
    const isRetryableError = isNetworkError(err) ||
                            (err.message && err.message.includes('timeout')) ||
                            (err.message && err.message.includes('ECONNRESET')) ||
                            (err.message && err.message.includes('socket hang up'));

    if (isRetryableError && updateState.retryCount < updateState.maxRetries) {
      console.log('检测到可重试错误，准备重试...', err.message);
      await retryUpdateDownload();
    } else {
      // 不是可重试错误或已达到重试上限
      updateState.retrying = false;
      if (mainWindow) {
        mainWindow.webContents.send('update-error', {
          message: err && (err.message || String(err)),
          isNetworkError: isNetworkError(err),
          retryCount: updateState.retryCount,
          canRetry: updateState.retryCount < updateState.maxRetries
        });
      }
    }
  });

  autoUpdater.on('download-progress', (progressObj) => {
    const speed = (progressObj.bytesPerSecond / 1024 / 1024).toFixed(2);
    const transferred = (progressObj.transferred / 1024 / 1024).toFixed(2);
    const total = (progressObj.total / 1024 / 1024).toFixed(2);

    let log_message = `下载进度: ${progressObj.percent.toFixed(1)}% `;
    log_message += `(${transferred}MB / ${total}MB) `;
    log_message += `速度: ${speed}MB/s `;
    log_message += `增量: ${(progressObj.delta / 1024).toFixed(1)}KB`;

    console.log(log_message);

    // 检测进度是否重置（可能是重新开始下载）
    if (updateState.progress && progressObj.percent < updateState.progress.percent) {
      console.warn('⚠️ 检测到下载进度重置！从', updateState.progress.percent.toFixed(1), '% 重置到', progressObj.percent.toFixed(1), '%');
    }

    updateState.downloading = true;
    updateState.progress = progressObj;

    if (mainWindow) {
      mainWindow.webContents.send('update-download-progress', progressObj);
    }
  });

  autoUpdater.on('update-downloaded', (info) => {
    console.log('更新下载完成');
    updateState.downloading = false;
    updateState.downloaded = true;
    if (mainWindow) {
      mainWindow.webContents.send('update-downloaded', info);
    }

    // 新的更新流程：下载完成后弹出安装提示
    try {
      if (appSettings.autoUpdate !== false) {
        // 自动更新开启时，弹出安装提示
        dialog.showMessageBox(mainWindow, {
          type: 'info',
          title: '更新下载完成',
          message: `新版本 ${info.version} 已下载完成，是否立即安装？`,
          buttons: ['立即安装', '稍后安装'],
          defaultId: 0,
          cancelId: 1
        }).then((result) => {
          if (result.response === 0) {
            // 用户选择立即安装
            autoUpdater.quitAndInstall();
          }
        });
      } else {
        // 自动更新关闭时，由update-card处理安装提示
        console.log('自动更新已关闭，由用户界面处理安装提示');
      }
    } catch (e) {
      console.warn('显示安装提示失败:', e);
    }
  });
}

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: getWindowConfig('login').width,
    height: getWindowConfig('login').height,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, '../../assets/icon.png'),
    show: false, // 先不显示，等加载完成后再显示
    resizable: false, // 登录窗口不可调整大小
    center: true
  });

  // 设置全局引用
  global.mainWindow = mainWindow;

  // 设置全局引用
  global.mainWindow = mainWindow;

  // 首先加载登录界面
  mainWindow.loadFile(path.join(__dirname, '../renderer/pages/login.html'));

  // 窗口加载完成后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // 处理窗口关闭事件
  mainWindow.on('close', (event) => {
    // 如果启用了最小化到托盘且不是强制退出，则阻止窗口关闭并隐藏到托盘
    if (appSettings.minimizeToTray && tray && !app.isQuiting) {
      event.preventDefault();
      mainWindow.hide();
      return;
    }
  });

  // 当 window 被关闭，这个事件会被触发
  mainWindow.on('closed', () => {
    // 取消引用 window 对象，如果你的应用支持多窗口的话，
    // 通常会把多个 window 对象存放在一个数组里面，
    // 与此同时，你应该删除相应的元素。
    mainWindow = null;
    global.mainWindow = null;
    currentUser = null;
  });

  // 开发环境下打开开发者工具
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }
}

// 切换到主界面
function switchToMainApp() {
  if (!mainWindow) return;

  // 调整窗口大小和属性
  mainWindow.setSize(getWindowConfig('main').width, getWindowConfig('main').height);
  mainWindow.setResizable(true);
  mainWindow.center();

  // 加载主应用界面
  mainWindow.loadFile(path.join(__dirname, '../renderer/pages/index.html'));
}

// 切换到管理界面
function switchToAdminPanel() {
  if (!mainWindow) return;

  // 调整窗口大小和属性
  mainWindow.setSize(getWindowConfig('admin').width, getWindowConfig('admin').height);
  mainWindow.setResizable(true);
  mainWindow.center();

  // 加载管理界面
  mainWindow.loadFile(path.join(__dirname, '../renderer/pages/admin.html'));
}

// 切换到设置界面
function switchToSettings() {
  if (!mainWindow) return;

  // 调整窗口大小和属性
  mainWindow.setSize(getWindowConfig('settings').width, getWindowConfig('settings').height);
  mainWindow.setResizable(true);
  mainWindow.center();

  // 加载设置界面
  mainWindow.loadFile(path.join(__dirname, '../renderer/pages/settings.html'));
}

// 切换到登录界面
function switchToLogin() {
  if (!mainWindow) return;

  // 调整窗口大小和属性
  mainWindow.setSize(getWindowConfig('login').width, getWindowConfig('login').height);
  mainWindow.setResizable(false);
  mainWindow.center();

  // 加载登录界面
  mainWindow.loadFile(path.join(__dirname, '../renderer/pages/login.html'));
}

// Electron 会在初始化后并准备
// 创建浏览器窗口时，调用这个函数。
// ====== 更新相关 IPC ======

// 渲染进程请求开始下载更新（当 autoDownload=false 时使用）
ipcMain.handle('start-update-download', async () => {
  try {
    console.log('🚀 开始启动更新下载...');

    updateState.downloading = true;
    updateState.progress = null;
    updateState.retryCount = 0;
    updateState.retrying = false;

    // 重置下载状态
    resetDownloadState();

    console.log('📥 使用增强的下载功能（支持断点续传）...');

    // 使用增强的下载功能
    const result = await downloadWithRetry(5, 600000); // 5次重试，每次10分钟超时

    console.log('✅ 下载完成:', result);
    updateState.downloading = false;

    // 发送下载完成事件
    if (mainWindow) {
      mainWindow.webContents.send('update-downloaded', result);
    }

    return { success: true, result };
  } catch (e) {
    console.error('❌ 启动下载失败:', e);
    updateState.downloading = false;

    // 发送错误事件
    if (mainWindow) {
      mainWindow.webContents.send('update-error', {
        message: e.message,
        isNetworkError: isNetworkError(e) || isRetryableError(e)
      });
    }

    return { success: false, error: e.message };
  }
});

// 渲染进程请求立即安装
ipcMain.handle('install-update-now', () => {
  try {
    autoUpdater.quitAndInstall();
    return { success: true };
  } catch (e) {
    return { success: false, error: e.message };
  }
});

// 手动重试更新下载
ipcMain.handle('retry-update-download', async () => {
  try {
    // 重置重试计数
    updateState.retryCount = 0;
    updateState.retrying = false;
    updateState.lastError = null;

    console.log('用户手动重试更新下载');

    // 检查网络连接
    const isConnected = await checkNetworkConnection();
    if (!isConnected) {
      return {
        success: false,
        error: '网络连接不可用，请检查网络设置'
      };
    }

    // 重置下载状态
    resetDownloadState();

    // 开始下载
    updateState.downloading = true;
    await downloadWithRetry(5, 600000); // 使用增强的下载功能
    return { success: true };
  } catch (error) {
    console.error('手动重试下载失败:', error);
    updateState.downloading = false;
    return {
      success: false,
      error: error.message,
      isNetworkError: isNetworkError(error) || isRetryableError(error)
    };
  }
});

// 获取下载状态
ipcMain.handle('get-download-state', () => {
  return getDownloadState();
});

// 清理下载
ipcMain.handle('cleanup-download', () => {
  try {
    cleanupDownload();
    updateState.downloading = false;
    updateState.retrying = false;
    return { success: true };
  } catch (error) {
    console.error('清理下载失败:', error);
    return { success: false, error: error.message };
  }
});

// 获取更新状态
ipcMain.handle('get-update-state', () => {
  return {
    ...updateState,
    // 不返回敏感信息
    lastError: updateState.lastError ? {
      message: updateState.lastError.message,
      isNetworkError: isNetworkError(updateState.lastError)
    } : null
  };
});

// 重置更新状态
ipcMain.handle('reset-update-state', () => {
  updateState.retryCount = 0;
  updateState.retrying = false;
  updateState.lastError = null;
  updateState.downloading = false;
  updateState.progress = null;
  return { success: true };
});

// 监听设置更改通知
ipcMain.on('settings-changed', (event, changedSettings) => {
  console.log('主进程收到设置更改通知:', changedSettings);

  // 更新本地设置缓存
  Object.assign(appSettings, changedSettings);

  // 如果自动更新设置发生变化，重新配置autoUpdater
  if (changedSettings.hasOwnProperty('autoUpdate')) {
    autoUpdater.autoDownload = changedSettings.autoUpdate !== false;
    console.log('autoUpdater.autoDownload已更新为:', autoUpdater.autoDownload);
  }

  // 广播设置更改到所有渲染进程
  if (mainWindow) {
    mainWindow.webContents.send('settings-changed', changedSettings);
  }
});

// 创建系统托盘
function createTray() {
  if (tray) return; // 如果托盘已存在，不重复创建

  try {
    // 优先使用.ico文件（Windows系统托盘推荐格式），回退到.png
    let iconPath = path.join(__dirname, '../../assets/icon.ico');
    if (!fs.existsSync(iconPath)) {
      iconPath = path.join(__dirname, '../../assets/icon.png');
      if (!fs.existsSync(iconPath)) {
        console.error('托盘图标文件不存在:', iconPath);
        return;
      }
    }

    console.log('创建系统托盘，使用图标:', iconPath);
    tray = new Tray(iconPath);

    // 设置托盘提示
    tray.setToolTip('AI重器');

    // 显示窗口的函数
    const showWindow = () => {
      if (mainWindow) {
        if (mainWindow.isMinimized()) mainWindow.restore();
        mainWindow.show();
        mainWindow.focus();
      }
    };

    // 创建托盘菜单
    const contextMenu = Menu.buildFromTemplate([
      {
        label: '显示窗口',
        click: showWindow
      },
      {
        label: '隐藏窗口',
        click: () => {
          if (mainWindow) {
            mainWindow.hide();
          }
        }
      },
      { type: 'separator' },
      {
        label: '退出应用',
        click: () => {
          // 强制退出，不触发最小化到托盘
          app.isQuiting = true;
          app.quit();
        }
      }
    ]);

    tray.setContextMenu(contextMenu);

    // 单击托盘图标显示窗口（Windows推荐）
    tray.on('click', showWindow);

    // 双击托盘图标显示窗口（兼容性）
    tray.on('double-click', showWindow);

    console.log('系统托盘创建成功');
  } catch (error) {
    console.error('创建系统托盘失败:', error);
    tray = null;
  }
}

// 销毁系统托盘
function destroyTray() {
  if (tray) {
    tray.destroy();
    tray = null;
  }
}

// 应用开机自启动设置
function applyAutoStartSetting() {
  try {
    app.setLoginItemSettings({
      openAtLogin: appSettings.autoStart || false,
      path: process.execPath
    });
    console.log('开机自启动设置已应用:', appSettings.autoStart);
  } catch (error) {
    console.error('设置开机自启动失败:', error);
  }
}

// 部分 API 在 ready 事件触发后才能使用。
app.whenReady().then(async () => {
  // 打印环境配置信息（仅开发环境）
  printEnvConfig();

  // 先加载应用设置
  await loadAppSettings();

  // 然后初始化国际化
  await initializeI18n();

  // 应用开机自启动设置
  applyAutoStartSetting();

  // 如果启用了系统托盘，创建托盘
  if (appSettings.minimizeToTray) {
    console.log('应用启动时创建系统托盘，设置值:', appSettings.minimizeToTray);
    createTray();
  } else {
    console.log('系统托盘功能未启用');
  }

  createWindow();

  // 配置自动更新
  configureAutoUpdater();

  app.on('activate', () => {

    // 在macOS上，当单击dock图标并且没有其他窗口打开时，
    // 通常在应用程序中重新创建一个窗口。
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// 当全部窗口关闭时退出。
app.on('window-all-closed', () => {
  // 在 macOS 上，除非用户用 Cmd + Q 确定地退出，
  // 否则绝大部分应用及其菜单栏会保持激活。
  if (process.platform !== 'darwin') app.quit();
});

// 手动检查更新
ipcMain.handle('check-for-updates', async () => {
  if (!isDev) {
    try {
      const result = await autoUpdater.checkForUpdates();
      return result;
    } catch (error) {
      console.error('检查更新失败:', error);
      return null;
    }
  }
  return null;
});

// 获取应用版本
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

// 获取应用名称
ipcMain.handle('get-app-name', () => {
  const packageJson = require('../../package.json');
  return packageJson.build?.productName || packageJson.name;
});

// 获取构建日期
ipcMain.handle('get-build-date', () => {
  // 尝试从环境变量获取构建日期，如果没有则使用当前日期
  const buildDate = process.env.BUILD_DATE || new Date().toISOString().split('T')[0];
  return buildDate;
});

// 配置相关 IPC 处理
ipcMain.handle('get-env-var', (event, key) => {
  // 兼容旧的调用方式，但建议使用新的配置结构
  return APP_CONFIG[key] || null;
});

ipcMain.handle('get-api-config', () => {
  const { getConfig } = require('../shared/config/api');
  return getConfig();
});

ipcMain.handle('get-app-config', () => {
  const packageJson = require('../../package.json');
  return {
    name: packageJson.build?.productName || packageJson.name,
    version: packageJson.version,
    environment: APP_CONFIG.NODE_ENV,
    theme: APP_CONFIG.UI.DEFAULT_THEME,
    language: APP_CONFIG.UI.DEFAULT_LANGUAGE,
    debugMode: APP_CONFIG.DEBUG.ENABLED,
    devToolsEnabled: APP_CONFIG.UI.DEV_TOOLS_ENABLED
  };
});

ipcMain.handle('is-development', () => {
  return isDevelopment();
});

ipcMain.handle('get-window-config', (event, windowType) => {
  return getWindowConfig(windowType);
});

// 获取当前用户信息
ipcMain.handle('get-current-user', () => {
  return currentUser;
});

// 验证授权码
ipcMain.handle('verify-auth-code', async (event, authCode) => {
  try {
    console.log('开始验证授权码:', authCode);
    return new Promise((resolve) => {
      const { getApiBaseUrl } = require('../shared/config/env');
      const apiBaseUrl = getApiBaseUrl();
      const request = net.request({
        method: 'POST',
        url: `${apiBaseUrl}/api/users/auth`
      });

      request.setHeader('Content-Type', 'application/json');

      let responseData = '';

      request.on('response', (response) => {
        response.on('data', (chunk) => {
          responseData += chunk;
        });

        response.on('end', () => {
          try {
            const result = JSON.parse(responseData);
            console.log('服务器响应:', result);

            if (result.status === 'success') {
              // 将认证码添加到用户对象中，确保一致的字段名
              currentUser = {
                ...result.data.user,
                authCode: authCode  // 统一使用 authCode 字段
              };
              resolve({
                success: true,
                user: currentUser,
                message: result.message || '授权验证成功'
              });
            } else {
              resolve({
                success: false,
                error: result.message || result.error || '授权验证失败'
              });
            }
          } catch (parseError) {
            console.error('解析响应失败:', parseError);
            console.error('原始响应数据:', responseData);
            resolve({
              success: false,
              error: '服务器响应格式错误'
            });
          }
        });
      });

      request.on('error', (error) => {
        console.error('网络请求失败:', error);
        resolve({
          success: false,
          error: '无法连接到服务器，请确保后端服务正在运行'
        });
      });

      request.write(JSON.stringify({ authCode }));
      request.end();
    });
  } catch (error) {
    console.error('授权验证失败:', error);
    return {
      success: false,
      error: '授权验证过程中发生错误'
    };
  }
});

// 授权成功后切换到主界面
ipcMain.handle('auth-success', async (event, user) => {
  try {
    currentUser = user;
    switchToMainApp();
    return { success: true };
  } catch (error) {
    console.error('切换到主界面失败:', error);
    return { success: false, error: '切换到主界面失败' };
  }
});

// 打开管理面板
ipcMain.handle('open-admin-panel', async () => {
  try {
    // 检查用户权限
    if (!currentUser) {
      return {
        success: false,
        error: '请先登录'
      };
    }

    if (currentUser.userType !== 'admin') {
      return {
        success: false,
        error: '只有管理员才能访问管理面板'
      };
    }

    // 切换到内置管理界面
    switchToAdminPanel();

    return {
      success: true,
      message: '管理面板已打开'
    };
  } catch (error) {
    console.error('打开管理面板失败:', error);
    return {
      success: false,
      error: '打开管理面板失败'
    };
  }
});

// 切换到主界面
ipcMain.handle('switch-to-main', async () => {
  try {
    switchToMainApp();
    return { success: true };
  } catch (error) {
    console.error('切换到主界面失败:', error);
    return { success: false, error: '切换到主界面失败' };
  }
});

// 退出登录
ipcMain.handle('logout', async () => {
  try {
    // 清除当前用户信息
    currentUser = null;

    // 切换回登录界面
    switchToLogin();

    return { success: true };
  } catch (error) {
    console.error('退出登录失败:', error);
    return { success: false, error: '退出登录失败' };
  }
});

// 打开设置页面
ipcMain.handle('open-settings', async () => {
  try {
    switchToSettings();
    return { success: true };
  } catch (error) {
    console.error('打开设置页面失败:', error);
    return { success: false, error: '打开设置页面失败' };
  }
});

// 初始化国际化
async function initializeI18n() {
  try {
    const language = appSettings.language || 'zh-CN';
    await i18n.init(language);
    console.log(`国际化初始化完成，当前语言: ${language}`);
  } catch (error) {
    console.error('国际化初始化失败:', error);
    await i18n.init('zh-CN'); // 回退到中文
  }
}

// 加载应用设置
async function loadAppSettings() {
  try {
    const settingsPath = path.join(app.getPath('userData'), 'settings.json');
    console.log('设置文件路径:', settingsPath);

    if (fs.existsSync(settingsPath)) {
      const settingsData = fs.readFileSync(settingsPath, 'utf8');
      appSettings = JSON.parse(settingsData);
      console.log('从文件加载的设置:', appSettings);
    } else {
      // 使用默认设置
      appSettings = {
        language: 'zh-CN',
        autoStart: false,
        minimizeToTray: false,
        autoUpdate: true,
        theme: 'auto',
        updateChannel: 'custom',
        customUpdateUrl: 'https://aitools.vvvlin.com/uploads',
        skippedVersions: []
      };
      await saveAppSettings();
      console.log('使用默认设置:', appSettings);
    }
    console.log('应用设置已加载:', appSettings);
  } catch (error) {
    console.error('加载应用设置失败:', error);
    appSettings = {
      language: 'zh-CN',
      autoStart: false,
      minimizeToTray: false,
      autoUpdate: true,
      theme: 'auto',
      skippedVersions: []
    };
  }
}

// 保存应用设置
async function saveAppSettings() {
  try {
    const settingsPath = path.join(app.getPath('userData'), 'settings.json');
    fs.writeFileSync(settingsPath, JSON.stringify(appSettings, null, 2));
    console.log('应用设置已保存');
  } catch (error) {
    console.error('保存应用设置失败:', error);
  }
}

// 获取单个设置
ipcMain.handle('get-setting', async (event, key) => {
  return appSettings[key];
});

// 获取所有设置
ipcMain.handle('get-all-settings', async () => {
  return appSettings;
});

// 检查托盘状态
ipcMain.handle('check-tray-status', async () => {
  return {
    trayExists: !!tray,
    minimizeToTrayEnabled: appSettings.minimizeToTray,
    trayTooltip: tray ? tray.getToolTip() : null
  };
});

// 设置单个设置项
ipcMain.handle('set-setting', async (event, key, value) => {
  try {
    appSettings[key] = value;
    await saveAppSettings();

    // 立即应用相关设置
    if (key === 'autoStart') {
      applyAutoStartSetting();
    } else if (key === 'minimizeToTray') {
      if (value) {
        createTray();
        // 检查托盘是否创建成功
        if (!tray) {
          return {
            success: false,
            error: '系统托盘创建失败，请检查图标文件是否存在'
          };
        }
      } else {
        destroyTray();
      }
    }

    return { success: true };
  } catch (error) {
    console.error('保存设置失败:', error);
    return { success: false, error: error.message };
  }
});

// 保存所有设置
ipcMain.handle('save-all-settings', async (event, settings) => {
  try {
    const oldSettings = { ...appSettings };
    appSettings = { ...settings };
    await saveAppSettings();

    // 应用设置变更
    if (oldSettings.autoStart !== settings.autoStart) {
      applyAutoStartSetting();
    }

    if (oldSettings.minimizeToTray !== settings.minimizeToTray) {
      if (settings.minimizeToTray) {
        createTray();
        // 检查托盘是否创建成功
        if (!tray) {
          return {
            success: false,
            error: '系统托盘创建失败，请检查图标文件是否存在'
          };
        }
      } else {
        destroyTray();
      }
    }

    return { success: true };
  } catch (error) {
    console.error('保存所有设置失败:', error);
    return { success: false, error: error.message };
  }
});



// 获取国际化文本
ipcMain.handle('get-i18n-texts', async (event, language) => {
  try {
    console.log('主进程收到获取国际化文本请求，语言:', language);
    console.log('主进程当前语言:', i18n.getCurrentLanguage());

    if (language && language !== i18n.getCurrentLanguage()) {
      console.log('切换语言从', i18n.getCurrentLanguage(), '到', language);
      await i18n.switchLanguage(language);
    }

    const texts = i18n.getAllTexts();
    console.log('主进程返回的文本键:', Object.keys(texts).slice(0, 10));
    console.log('app.title文本:', texts['app.title']);
    console.log('使用t方法获取app.title:', i18n.t('app.title'));
    console.log('texts.app:', texts.app);
    console.log('texts.app.title:', texts.app ? texts.app.title : 'app对象不存在');

    return texts;
  } catch (error) {
    console.error('获取国际化文本失败:', error);
    return {};
  }
});

// 获取可用语言列表
ipcMain.handle('get-available-languages', async () => {
  return i18n.getAvailableLanguages();
});

// 翻译文本
ipcMain.handle('translate', async (event, key, params) => {
  return i18n.t(key, params);
});

// 应用主题
ipcMain.handle('apply-theme', async (event, theme) => {
  try {
    // 更新设置
    appSettings.theme = theme;
    await saveAppSettings();

    // 通知所有窗口主题已更改
    BrowserWindow.getAllWindows().forEach(window => {
      window.webContents.send('theme-changed', theme);
    });

    return { success: true };
  } catch (error) {
    console.error('应用主题失败:', error);
    return { success: false, error: error.message };
  }
});

// 支持更新通道的检查更新
ipcMain.handle('check-for-updates-with-channel', async (event, options) => {
  try {
    const { channel, customUrl } = options;

    // 根据通道选择更新源
    let updateUrl;
    switch (channel) {
      case 'github':
        updateUrl = 'https://github.com/laixiao/AiTools/releases';
        break;
      case 'gitee':
        updateUrl = 'https://gitee.com/laixiao/AiTools/releases';
        break;
      case 'custom':
        updateUrl = customUrl;
        break;
      default:
        updateUrl = 'https://github.com/laixiao/AiTools/releases';
    }

    if (!updateUrl) {
      return { success: false, error: '更新地址未配置' };
    }

    console.log(`检查更新，通道: ${channel}, 地址: ${updateUrl}`);

    // 在开发模式下模拟检查更新
    if (isDev) {
      return {
        success: true,
        hasUpdate: false,
        message: '开发模式下的模拟检查更新',
        channel: channel,
        url: updateUrl
      };
    }

    // 在生产模式下使用 autoUpdater
    return new Promise((resolve) => {
      // 设置更新源
      if (channel === 'custom' && customUrl) {
        autoUpdater.setFeedURL({ provider: 'generic', url: customUrl });
      } else {
        autoUpdater.setFeedURL({
          provider: 'github',
          owner: 'laixiao',
          repo: 'AiTools'
        });
      }

      // 监听更新事件
      const onUpdateAvailable = (info) => {
        autoUpdater.removeAllListeners();
        resolve({
          success: true,
          hasUpdate: true,
          version: info.version,
          channel: channel,
          url: updateUrl
        });
      };

      const onUpdateNotAvailable = () => {
        autoUpdater.removeAllListeners();
        resolve({
          success: true,
          hasUpdate: false,
          channel: channel,
          url: updateUrl
        });
      };

      const onError = (error) => {
        autoUpdater.removeAllListeners();
        resolve({
          success: false,
          error: error.message,
          channel: channel,
          url: updateUrl
        });
      };

      autoUpdater.once('update-available', onUpdateAvailable);
      autoUpdater.once('update-not-available', onUpdateNotAvailable);
      autoUpdater.once('error', onError);

      // 开始检查更新
      autoUpdater.checkForUpdates();

      // 10秒超时
      setTimeout(() => {
        autoUpdater.removeAllListeners();
        resolve({
          success: false,
          error: '检查更新超时',
          channel: channel,
          url: updateUrl
        });
      }, 10000);
    });

  } catch (error) {
    console.error('检查更新失败:', error);
    return { success: false, error: error.message };
  }
});
