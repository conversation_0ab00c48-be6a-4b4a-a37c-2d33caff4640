/**
 * 签名验证模块
 * 集成到 electron-updater 中，在安装更新前验证 Sigstore 签名
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const https = require('https');

/**
 * 检查 cosign 是否可用
 */
function isCosignAvailable() {
  return new Promise((resolve) => {
    const cosign = spawn('cosign', ['version'], { stdio: 'pipe' });
    cosign.on('close', (code) => {
      resolve(code === 0);
    });
    cosign.on('error', () => {
      resolve(false);
    });
  });
}

/**
 * 下载签名文件
 * @param {string} fileUrl - 原文件的下载URL
 * @param {string} outputPath - 签名文件保存路径
 * @param {string} suffix - 签名文件后缀 (.sig 或 .crt)
 */
function downloadSignatureFile(fileUrl, outputPath, suffix) {
  return new Promise((resolve, reject) => {
    const signatureUrl = fileUrl + suffix;
    
    console.log(`下载签名文件: ${signatureUrl}`);
    
    const file = fs.createWriteStream(outputPath);
    
    https.get(signatureUrl, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`下载签名文件失败: HTTP ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`签名文件下载完成: ${path.basename(outputPath)}`);
        resolve();
      });
    }).on('error', (error) => {
      fs.unlink(outputPath, () => {}); // 删除不完整的文件
      reject(error);
    });
  });
}

/**
 * 验证文件的 Sigstore 签名
 * @param {string} filePath - 要验证的文件路径
 * @param {string} signaturePath - 签名文件路径
 * @param {string} certificatePath - 证书文件路径
 */
function verifyFileSignature(filePath, signaturePath, certificatePath) {
  return new Promise((resolve, reject) => {
    console.log(`验证文件签名: ${path.basename(filePath)}`);
    
    const cosign = spawn('cosign', [
      'verify-blob',
      '--signature', signaturePath,
      '--certificate', certificatePath,
      '--certificate-identity-regexp', '.*',
      '--certificate-oidc-issuer-regexp', '.*',
      filePath
    ], {
      stdio: 'pipe',
      env: {
        ...process.env,
        COSIGN_EXPERIMENTAL: '1'
      }
    });

    let stdout = '';
    let stderr = '';

    cosign.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    cosign.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    cosign.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ 签名验证成功: ${path.basename(filePath)}`);
        resolve(true);
      } else {
        console.error(`❌ 签名验证失败: ${path.basename(filePath)}`);
        console.error('错误详情:', stderr);
        resolve(false);
      }
    });

    cosign.on('error', (error) => {
      console.error(`执行 cosign 验证时出错:`, error.message);
      reject(error);
    });
  });
}

/**
 * 计算文件的 SHA256 哈希值
 * @param {string} filePath - 文件路径
 */
function calculateFileHash(filePath) {
  return new Promise((resolve, reject) => {
    const hash = crypto.createHash('sha256');
    const stream = fs.createReadStream(filePath);
    
    stream.on('data', (data) => {
      hash.update(data);
    });
    
    stream.on('end', () => {
      resolve(hash.digest('hex'));
    });
    
    stream.on('error', (error) => {
      reject(error);
    });
  });
}

/**
 * 验证更新文件的签名
 * @param {string} updateFilePath - 更新文件路径
 * @param {string} downloadUrl - 原始下载URL（用于获取签名文件）
 * @returns {Promise<Object>} 验证结果
 */
async function verifyUpdateSignature(updateFilePath, downloadUrl) {
  const result = {
    verified: false,
    error: null,
    fileHash: null,
    cosignAvailable: false
  };

  try {
    // 检查文件是否存在
    if (!fs.existsSync(updateFilePath)) {
      throw new Error(`更新文件不存在: ${updateFilePath}`);
    }

    // 计算文件哈希
    result.fileHash = await calculateFileHash(updateFilePath);
    console.log(`文件哈希: ${result.fileHash}`);

    // 检查 cosign 是否可用
    result.cosignAvailable = await isCosignAvailable();
    
    if (!result.cosignAvailable) {
      console.warn('⚠️  cosign 不可用，跳过签名验证');
      result.error = 'cosign 不可用';
      return result;
    }

    // 准备签名文件路径
    const signaturePath = updateFilePath + '.sig';
    const certificatePath = updateFilePath + '.crt';

    try {
      // 下载签名文件
      await downloadSignatureFile(downloadUrl, signaturePath, '.sig');
      await downloadSignatureFile(downloadUrl, certificatePath, '.crt');

      // 验证签名
      result.verified = await verifyFileSignature(updateFilePath, signaturePath, certificatePath);

      // 清理临时签名文件
      fs.unlink(signaturePath, () => {});
      fs.unlink(certificatePath, () => {});

    } catch (downloadError) {
      console.warn('⚠️  无法下载签名文件，可能是旧版本发布:', downloadError.message);
      result.error = '签名文件不可用';
    }

  } catch (error) {
    console.error('签名验证过程中出错:', error.message);
    result.error = error.message;
  }

  return result;
}

/**
 * 为 electron-updater 提供的验证钩子
 * @param {Object} updateInfo - 更新信息
 * @param {string} downloadedFilePath - 下载的文件路径
 * @returns {Promise<boolean>} 是否允许安装
 */
async function validateUpdate(updateInfo, downloadedFilePath) {
  console.log('🔍 开始验证更新文件签名...');
  
  try {
    // 构建下载URL（假设使用 GitHub releases）
    const downloadUrl = `https://github.com/laixiao/AiTools/releases/download/v${updateInfo.version}/${path.basename(downloadedFilePath)}`;
    
    const verificationResult = await verifyUpdateSignature(downloadedFilePath, downloadUrl);
    
    if (verificationResult.verified) {
      console.log('🎉 更新文件签名验证成功！');
      return true;
    } else if (!verificationResult.cosignAvailable) {
      console.warn('⚠️  签名验证工具不可用，允许安装（建议安装 cosign）');
      return true; // 在 cosign 不可用时允许安装，但记录警告
    } else {
      console.error('❌ 更新文件签名验证失败！');
      
      // 在生产环境中，可以选择阻止安装
      const isDev = process.env.NODE_ENV === 'development' || process.argv.includes('--dev');
      if (isDev) {
        console.warn('🔧 开发模式：允许安装未验证的更新');
        return true;
      }
      
      return false; // 生产环境中阻止安装未验证的更新
    }
  } catch (error) {
    console.error('验证更新时出错:', error.message);
    
    // 出错时的策略：在开发模式允许，生产模式阻止
    const isDev = process.env.NODE_ENV === 'development' || process.argv.includes('--dev');
    return isDev;
  }
}

/**
 * 配置 electron-updater 的签名验证
 * @param {Object} autoUpdater - electron-updater 实例
 */
function configureSignatureVerification(autoUpdater) {
  // 注意：electron-updater 目前没有直接的签名验证钩子
  // 这里提供一个包装函数，可以在下载完成后调用
  
  const originalQuitAndInstall = autoUpdater.quitAndInstall.bind(autoUpdater);
  
  autoUpdater.quitAndInstall = async function(isSilent, isForceRunAfter) {
    console.log('🔒 在安装前进行签名验证...');
    
    try {
      // 获取下载的文件路径（这需要根据实际情况调整）
      const downloadedFile = autoUpdater.downloadedUpdateHelper?.file;
      
      if (downloadedFile && fs.existsSync(downloadedFile)) {
        const updateInfo = autoUpdater.updateInfoAndProvider?.info;
        const isValid = await validateUpdate(updateInfo, downloadedFile);
        
        if (!isValid) {
          throw new Error('更新文件签名验证失败，安装已取消');
        }
      }
    } catch (error) {
      console.error('签名验证失败:', error.message);
      throw error;
    }
    
    // 验证通过，继续安装
    return originalQuitAndInstall(isSilent, isForceRunAfter);
  };
}

module.exports = {
  verifyUpdateSignature,
  validateUpdate,
  configureSignatureVerification,
  isCosignAvailable,
  calculateFileHash
};
