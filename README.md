# AI重器

一个跨平台的Electron桌面应用程序，集成了后端服务和品牌管理功能。

## 🚀 功能特性

- ✅ 跨平台支持 (Windows, macOS, Linux)
- ✅ 自动更新功能
- ✅ GitHub Actions 自动构建和发布（支持前后端分离）
- ✅ **Sigstore 免费代码签名** - 确保应用安全性和可信度
- ✅ 现代化的用户界面
- ✅ 开发者友好的配置
- ✅ 后端服务 (Node.js + Express)
- ✅ MongoDB 数据库集成
- ✅ 品牌管理系统
- ✅ 用户管理系统
- ✅ 授权码验证
- ✅ 权限控制 (普通用户/管理员)
- ✅ 集成管理界面

## 📦 安装和运行

### 开发环境

1. 克隆项目
```bash
git clone https://github.com/laixiao/AiTools.git
cd AiTools
```

2. 安装依赖
```bash
# 安装客户端依赖
cd client && npm install

# 安装服务端依赖
cd ../server && npm install
```

3. 运行应用
```bash
# 使用启动脚本（推荐）
start.bat

# 或者分别启动
start-server.bat  # 启动后端服务
start-client.bat  # 启动Electron应用

# 或者手动启动
cd server && npm start    # 启动后端服务
cd client && npm start    # 启动Electron应用
```

### 数据库配置

项目使用 MongoDB 数据库，连接信息在 `server/.env` 文件中：
```
MONGODB_URI=*************************************************************
```

首次运行时会自动初始化品牌和用户数据。

### 授权码

应用启动时需要输入授权码才能进入。默认授权码：
- **管理员**: `ADMIN2024`
- **普通用户**: `RDKKQF76AVX6WP4E`

### 构建应用

```bash
# 构建客户端应用
cd client
npm run build        # 构建所有平台
npm run build:win    # Windows
npm run build:mac    # macOS
npm run build:linux  # Linux

# 验证构建产物的签名（需要先安装 cosign）
npm run verify-dist  # 验证 dist 目录中的所有文件
npm run install-cosign  # 自动安装 cosign 工具
```

## 🎯 使用说明

### 使用流程

1. 启动后端服务：`cd server && npm start`
2. 启动Electron应用：`cd client && npm start`
3. 输入授权码登录
4. 管理员用户可以访问管理面板

### 管理功能

#### 品牌管理
- ✅ 查看品牌列表
- ✅ 添加新品牌
- ✅ 编辑品牌信息
- ✅ 删除品牌
- ✅ 启用/禁用品牌
- ✅ 排序管理

#### 用户管理 (仅管理员)
- ✅ 查看用户列表
- ✅ 添加新用户
- ✅ 编辑用户信息
- ✅ 删除用户
- ✅ 重新生成授权码
- ✅ 用户类型管理

## 🔧 配置说明

### 1. 修改应用信息

编辑 `package.json` 文件中的以下字段：

```json
{
  "name": "your-app-name",
  "version": "1.0.0",
  "description": "你的应用描述",
  "homepage": "https://github.com/yourusername/your-app-name",
  "author": {
    "name": "Your Name",
    "email": "<EMAIL>"
  },
  "build": {
    "appId": "com.yourcompany.yourappname",
    "productName": "Your App Name",
    "publish": {
      "provider": "github",
      "owner": "yourusername",
      "repo": "your-app-name"
    }
  }
}
```

### 2. 添加应用图标

在 `assets` 文件夹中放置以下图标文件：
- `icon.png` (512x512 像素，用于 Linux)
- `icon.ico` (包含多种尺寸，用于 Windows)
- `icon.icns` (包含多种尺寸，用于 macOS)

可以使用在线工具转换图标格式：
- [PNG to ICO](https://convertio.co/png-ico/)
- [PNG to ICNS](https://convertio.co/png-icns/)

## 🚀 GitHub Actions 自动构建

### 设置步骤

1. **创建 GitHub 仓库**
   - 在 GitHub 上创建新仓库
   - 将代码推送到仓库

2. **配置仓库设置**
   - 进入仓库的 Settings → Actions → General
   - 确保 "Allow GitHub Actions to create and approve pull requests" 已启用

3. **推送代码触发构建**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

4. **创建发布版本**
   ```bash
   # 创建并推送标签来触发发布
   git tag v1.0.0
   git push origin v1.0.0
   ```

### 自动构建流程

- **推送到 main 分支**: 触发构建，生成构建产物
- **推送标签 (v*)**: 触发构建并自动创建 GitHub Release

## 🔄 自动更新功能

应用内置了自动更新功能，并集成了 **Sigstore 签名验证**：

1. **自动检查**: 应用启动时自动检查更新
2. **手动检查**: 点击"检查更新"按钮
3. **自动下载**: 发现新版本时自动下载
4. **签名验证**: 下载完成后自动验证 Sigstore 签名
5. **安全安装**: 验证通过后提示用户安装

### 更新流程

1. 修改 `package.json` 中的版本号
2. 提交代码并推送
3. 创建新的版本标签
4. GitHub Actions 自动构建、签名并发布
5. 用户的应用会自动检测到新版本并验证签名

### 🔒 安全特性

- **免费签名**: 使用 Sigstore 进行免费代码签名
- **自动验证**: 更新时自动验证文件完整性
- **透明日志**: 所有签名记录在公开的透明日志中
- **无需证书**: 无需购买昂贵的代码签名证书

详细的签名说明请参考：[SIGSTORE_SIGNING.md](SIGSTORE_SIGNING.md)

## 📝 版本发布流程

1. **更新版本号**
   ```bash
   npm version patch  # 补丁版本 (1.0.0 -> 1.0.1)
   npm version minor  # 次要版本 (1.0.0 -> 1.1.0)
   npm version major  # 主要版本 (1.0.0 -> 2.0.0)
   ```

2. **推送标签**
   ```bash
   git push origin main --tags
   ```

3. **等待自动构建**
   - GitHub Actions 会自动构建所有平台的安装包
   - 构建完成后会自动创建 GitHub Release

## 🛠️ 开发指南

### 项目结构

```
AiTools/
├── client/              # Electron 客户端
│   ├── src/
│   │   ├── main.js      # 主进程文件
│   │   ├── index.html   # 主界面
│   │   ├── login.html   # 登录界面
│   │   ├── admin.html   # 管理界面
│   │   ├── renderer.js  # 主界面逻辑
│   │   ├── login.js     # 登录逻辑
│   │   └── admin.js     # 管理界面逻辑
│   ├── assets/          # 应用图标和资源
│   ├── node_modules/    # 客户端依赖
│   └── package.json     # 客户端配置
├── server/              # Node.js 后端服务
│   ├── models/          # 数据模型
│   │   ├── Brand.js     # 品牌模型
│   │   └── User.js      # 用户模型
│   ├── routes/          # API 路由
│   │   ├── brands.js    # 品牌路由
│   │   └── users.js     # 用户路由
│   ├── scripts/         # 脚本文件
│   │   └── init-data.js # 初始化数据
│   ├── node_modules/    # 服务端依赖
│   ├── app.js           # 服务器入口
│   ├── .env             # 环境配置
│   └── package.json     # 服务端配置
├── .github/workflows/   # GitHub Actions 配置
├── start.bat            # 完整启动脚本
├── start-client.bat     # 客户端启动脚本
├── start-server.bat     # 服务端启动脚本
└── README.md           # 说明文档
```

### 添加新功能

#### 前端 (Electron)
1. 修改 `client/src/index.html` 添加 UI 元素
2. 在 `client/src/renderer.js` 中添加前端逻辑
3. 在 `client/src/main.js` 中添加主进程逻辑
4. 使用 IPC 通信连接前端和后端

#### 后端 (Express)
1. 在 `server/models/` 中添加数据模型
2. 在 `server/routes/` 中添加 API 路由
3. 在 `server/public/` 中添加管理界面
4. 使用 MongoDB 进行数据持久化

## 🔌 API 接口

### 品牌管理 API

#### 品牌管理 API
- `GET /api/brands` - 获取品牌列表
- `GET /api/brands/:id` - 获取单个品牌
- `POST /api/brands` - 创建品牌
- `PUT /api/brands/:id` - 更新品牌
- `DELETE /api/brands/:id` - 删除品牌
- `PATCH /api/brands/sort` - 批量更新排序

#### 用户管理 API
- `GET /api/users` - 获取用户列表
- `GET /api/users/:id` - 获取单个用户
- `POST /api/users` - 创建用户
- `PUT /api/users/:id` - 更新用户
- `DELETE /api/users/:id` - 删除用户
- `POST /api/users/auth` - 授权码验证
- `PATCH /api/users/:id/regenerate-auth-code` - 重新生成授权码
- `PATCH /api/users/batch` - 批量操作用户

## 🔄 CI/CD 工作流

项目包含GitHub Actions自动构建工作流：

### 构建和发布 (build.yml)
- **触发条件**: 推送到main分支或创建tag
- **功能**:
  - 安装客户端依赖
  - 构建Electron应用
  - 生成Windows、macOS、Linux安装包
  - 自动发布到GitHub Releases

### 发布流程
1. 更新版本号：修改 `client/package.json` 中的版本
2. 创建tag：`git tag v1.0.5 && git push origin v1.0.5`
3. 自动构建：GitHub Actions自动构建并发布客户端安装包

**注意**: 后端服务需要手动部署到服务器

## 🐛 常见问题

### 1. 构建失败
- 检查 Node.js 版本 (推荐 18+)
- 确保所有依赖已正确安装
- 检查 GitHub Actions 日志

### 2. 自动更新不工作
- 确保应用已发布到 GitHub Releases
- 检查 `package.json` 中的 `publish` 配置
- 确保版本号格式正确 (v1.0.0)

### 3. 图标不显示
- 确保图标文件存在于 `assets` 文件夹
- 检查图标文件格式和尺寸
- 重新构建应用

## 📄 许可证

MIT License