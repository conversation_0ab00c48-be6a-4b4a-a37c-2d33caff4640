/**
 * 更新提示卡片工具模块
 * 提供跨页面的更新状态显示功能
 */

const { ipcRenderer } = require('electron');
const { downloadMonitor } = require('./download-monitor');

class UpdateCard {
  constructor() {
    this.cardEl = null;
    this.progressBarEl = null;
    this.progressTextEl = null;
    this.titleEl = null;
    this.actionContainerEl = null;
    this.autoUpdateEnabled = true;
    this.i18nTexts = {};
    this.skippedVersions = new Set();
    
    this.init();
  }

  async init() {
    try {
      // 获取自动更新设置
      this.autoUpdateEnabled = await ipcRenderer.invoke('get-setting', 'autoUpdate');
      
      // 获取跳过的版本列表
      const skipped = await ipcRenderer.invoke('get-setting', 'skippedVersions') || [];
      this.skippedVersions = new Set(skipped);
      
      // 获取国际化文本
      await this.loadI18nTexts();
    } catch (e) {
      console.warn('初始化更新卡片失败:', e);
      this.autoUpdateEnabled = true;
    }

    // 始终监听事件，但仅在关闭自动更新时展示UI
    this.setupEventListeners();
    
    // 加载已有状态（避免错过事件）
    this.loadExistingState();
  }

  async loadI18nTexts() {
    try {
      const language = await ipcRenderer.invoke('get-setting', 'language') || 'zh-CN';
      this.i18nTexts = await ipcRenderer.invoke('get-i18n-texts', language);
    } catch (e) {
      console.warn('加载国际化文本失败:', e);
      this.i18nTexts = {};
    }
  }

  setupEventListeners() {
    ipcRenderer.on('update-available', (event, info) => {
      if (this.autoUpdateEnabled === false && !this.isVersionSkipped(info.version)) {
        this.showUpdateCard(info);
      }
    });

    ipcRenderer.on('update-download-progress', (event, progress) => {
      // 只有在自动更新关闭时才显示进度UI
      if (this.autoUpdateEnabled === false) {
        this.showUpdateProgress(progress);
      }
    });

    ipcRenderer.on('update-downloaded', (event, info) => {
      // 只有在自动更新关闭时才显示安装提示UI
      if (this.autoUpdateEnabled === false) {
        this.showInstallPrompt();
      }
    });

    ipcRenderer.on('update-error', (event, errorInfo) => {
      if (this.autoUpdateEnabled === false) {
        // 处理新的错误信息格式
        const message = typeof errorInfo === 'string' ? errorInfo : errorInfo.message;
        const isNetworkError = typeof errorInfo === 'object' ? errorInfo.isNetworkError : false;
        const canRetry = typeof errorInfo === 'object' ? errorInfo.canRetry : false;

        this.showUpdateError(message || this.t('settings.update.card.download_failed', { error: 'Unknown error' }), {
          isNetworkError,
          canRetry,
          retryCount: errorInfo.retryCount || 0
        });
      }
    });

    // 监听重试状态
    ipcRenderer.on('update-retrying', (event, retryInfo) => {
      if (this.autoUpdateEnabled === false) {
        this.showRetryProgress(retryInfo);
      }
    });

    ipcRenderer.on('update-retry-failed', (event, failInfo) => {
      if (this.autoUpdateEnabled === false) {
        this.showRetryFailed(failInfo);
      }
    });

    // 监听语言变化
    ipcRenderer.on('language-changed', async () => {
      await this.loadI18nTexts();
      this.updateCardTexts();
    });
  }

  async loadExistingState() {
    try {
      const state = await ipcRenderer.invoke('get-update-state');
      if (state && this.autoUpdateEnabled === false) {
        if (state.available && state.info && !this.isVersionSkipped(state.info.version)) {
          this.showUpdateCard(state.info);
          if (state.downloading) {
            this.showUpdateProgress(state.progress || { percent: 0, bytesPerSecond: 0, transferred: 0, total: 0 });
          } else if (state.downloaded) {
            this.showInstallPrompt();
          }
        }
      }
    } catch (e) {
      console.warn('获取更新状态失败:', e);
    }
  }

  isVersionSkipped(version) {
    return this.skippedVersions.has(version);
  }

  async skipVersion(version) {
    try {
      this.skippedVersions.add(version);
      const skippedArray = Array.from(this.skippedVersions);
      await ipcRenderer.invoke('set-setting', 'skippedVersions', skippedArray);
      this.hideCard();
    } catch (e) {
      console.error('保存跳过版本失败:', e);
    }
  }

  ensureUpdateCardContainer() {
    if (this.cardEl) return this.cardEl;
    
    // 确保CSS已加载
    this.ensureCSS();
    
    this.cardEl = document.createElement('div');
    this.cardEl.className = 'update-card';

    const closeBtn = document.createElement('button');
    closeBtn.className = 'update-card-close';
    closeBtn.textContent = '✕';
    closeBtn.title = this.t('settings.update.card.buttons.close');
    closeBtn.onclick = () => this.hideCard();
    this.cardEl.appendChild(closeBtn);

    this.titleEl = document.createElement('div');
    this.titleEl.className = 'update-card-title';
    this.cardEl.appendChild(this.titleEl);

    this.progressBarEl = document.createElement('div');
    this.progressBarEl.className = 'update-progress-bar';
    const barInner = document.createElement('div');
    barInner.className = 'update-progress-inner';
    barInner.id = 'update-progress-inner';
    this.progressBarEl.appendChild(barInner);
    this.cardEl.appendChild(this.progressBarEl);

    this.progressTextEl = document.createElement('div');
    this.progressTextEl.className = 'update-progress-text';
    this.cardEl.appendChild(this.progressTextEl);

    this.actionContainerEl = document.createElement('div');
    this.actionContainerEl.className = 'update-actions';
    this.cardEl.appendChild(this.actionContainerEl);

    document.body.appendChild(this.cardEl);
    return this.cardEl;
  }

  ensureCSS() {
    if (document.getElementById('update-card-css')) return;
    
    const link = document.createElement('link');
    link.id = 'update-card-css';
    link.rel = 'stylesheet';
    link.href = '../assets/styles/update-card.css';
    document.head.appendChild(link);
  }

  showUpdateCard(info) {
    this.ensureUpdateCardContainer();
    this.titleEl.textContent = this.t('settings.update.card.new_version_found', { 
      version: info && info.version ? 'v' + info.version : '' 
    });

    this.actionContainerEl.innerHTML = '';
    
    const updateBtn = this.createButton(
      this.t('settings.update.card.buttons.update_now'),
      'update-btn update-btn-primary',
      async () => {
        updateBtn.disabled = true;
        updateBtn.innerHTML = `<span class="update-loading"></span>${this.t('settings.update.card.preparing')}`;
        try {
          const res = await ipcRenderer.invoke('start-update-download');
          if (!res || !res.success) {
            this.showUpdateError(res && res.error || this.t('settings.update.card.download_failed', { error: 'Unknown error' }));
            updateBtn.disabled = false;
            updateBtn.textContent = this.t('settings.update.card.buttons.update_now');
            return;
          }
          this.titleEl.textContent = this.t('settings.update.card.downloading');
          this.progressBarEl.style.display = 'block';
          this.progressTextEl.style.display = 'block';
        } catch (e) {
          this.showUpdateError(e.message || this.t('settings.update.card.download_failed', { error: 'Unknown error' }));
          updateBtn.disabled = false;
          updateBtn.textContent = this.t('settings.update.card.buttons.update_now');
        }
      }
    );

    const skipBtn = this.createButton(
      this.t('settings.update.card.buttons.skip_version'),
      'update-btn update-btn-skip',
      () => this.skipVersion(info && info.version)
    );

    const laterBtn = this.createButton(
      this.t('settings.update.card.buttons.later'),
      'update-btn update-btn-secondary',
      () => this.hideCard()
    );

    this.actionContainerEl.appendChild(laterBtn);
    this.actionContainerEl.appendChild(skipBtn);
    this.actionContainerEl.appendChild(updateBtn);
  }

  showUpdateProgress(p) {
    this.ensureUpdateCardContainer();
    this.titleEl.textContent = this.t('settings.update.card.downloading');
    this.progressBarEl.style.display = 'block';
    this.progressTextEl.style.display = 'block';

    const percent = Math.max(0, Math.min(100, Number(p && p.percent) || 0));
    const speed = (p && p.bytesPerSecond) || 0;
    const transferred = (p && p.transferred) || 0;
    const total = (p && p.total) || 0;

    const barInner = document.getElementById('update-progress-inner');
    if (barInner) barInner.style.width = percent.toFixed(1) + '%';

    // 计算ETA
    const eta = downloadMonitor.calculateETA(transferred, total, speed);

    this.progressTextEl.innerHTML = `
      <div>${percent.toFixed(1)}% - ${this.formatBytes(transferred)} / ${this.formatBytes(total)}</div>
      <div>速度: ${this.formatBytes(speed)}/s - 剩余时间: ${eta}</div>
    `;

    // 启动下载监控（如果还没启动）
    if (!downloadMonitor.isMonitoring) {
      this.startDownloadMonitoring();
    }
  }

  startDownloadMonitoring() {
    downloadMonitor.startMonitoring({
      onProgress: (progress) => {
        // 进度更新已由原有事件处理
      },
      onStalled: (info) => {
        console.warn('下载卡住:', info);
        this.showDownloadStalled(info);
      },
      onResumed: (info) => {
        console.log('下载恢复:', info);
        this.showDownloadResumed(info);
      },
      onError: (error) => {
        console.error('下载错误:', error);
        this.showDownloadError(error);
      },
      onComplete: (info) => {
        console.log('下载完成:', info);
        downloadMonitor.stopMonitoring();
      }
    });
  }

  showDownloadStalled(info) {
    this.titleEl.textContent = '下载似乎卡住了...';
    this.titleEl.style.color = '#ff9800';

    // 添加重试按钮
    if (!document.getElementById('retry-download-btn')) {
      const retryBtn = this.createButton(
        '重试下载',
        'update-btn update-btn-secondary',
        async () => {
          retryBtn.disabled = true;
          retryBtn.textContent = '重试中...';

          const result = await downloadMonitor.retryDownload();
          if (result.success) {
            retryBtn.remove();
            this.titleEl.textContent = this.t('settings.update.card.downloading');
            this.titleEl.style.color = '';
          } else {
            retryBtn.disabled = false;
            retryBtn.textContent = '重试下载';
          }
        }
      );
      retryBtn.id = 'retry-download-btn';
      this.actionContainerEl.appendChild(retryBtn);
    }
  }

  showDownloadResumed(info) {
    this.titleEl.textContent = this.t('settings.update.card.downloading');
    this.titleEl.style.color = '';

    // 移除重试按钮
    const retryBtn = document.getElementById('retry-download-btn');
    if (retryBtn) retryBtn.remove();
  }

  showDownloadError(error) {
    this.titleEl.textContent = '下载出错';
    this.titleEl.style.color = '#f44336';
    this.progressTextEl.textContent = error.message || '未知错误';
  }

  showInstallPrompt() {
    this.ensureUpdateCardContainer();
    this.cardEl.className = 'update-card success';
    this.titleEl.textContent = this.t('settings.update.card.download_complete');
    this.progressBarEl.style.display = 'none';
    this.progressTextEl.style.display = 'none';

    this.actionContainerEl.innerHTML = '';
    
    const installBtn = this.createButton(
      this.t('settings.update.card.buttons.install_now'),
      'update-btn update-btn-primary',
      async () => {
        installBtn.disabled = true;
        installBtn.innerHTML = `<span class="update-loading"></span>${this.t('settings.update.card.installing')}`;
        await ipcRenderer.invoke('install-update-now');
      }
    );

    const laterBtn = this.createButton(
      this.t('settings.update.card.buttons.later'),
      'update-btn update-btn-secondary',
      () => this.hideCard()
    );

    this.actionContainerEl.appendChild(laterBtn);
    this.actionContainerEl.appendChild(installBtn);
  }

  showUpdateError(message, options = {}) {
    this.ensureUpdateCardContainer();
    this.cardEl.className = 'update-card error';

    // 根据错误类型显示不同的标题
    if (options.isNetworkError) {
      this.titleEl.textContent = this.t('settings.update.card.network_error');
    } else {
      this.titleEl.textContent = this.t('settings.update.card.download_failed', { error: message });
    }

    this.progressBarEl.style.display = 'none';
    this.progressTextEl.style.display = 'none';

    // 显示错误详情
    if (!this.errorDetailsEl) {
      this.errorDetailsEl = document.createElement('div');
      this.errorDetailsEl.className = 'update-error-details';
      this.cardEl.insertBefore(this.errorDetailsEl, this.actionContainerEl);
    }

    this.errorDetailsEl.innerHTML = `
      <p class="error-message">${message}</p>
      ${options.isNetworkError ? '<p class="error-hint">请检查网络连接后重试</p>' : ''}
      ${options.retryCount > 0 ? `<p class="retry-info">已重试 ${options.retryCount} 次</p>` : ''}
    `;

    this.actionContainerEl.innerHTML = '';

    // 如果可以重试，显示重试按钮
    if (options.canRetry && options.isNetworkError) {
      const retryBtn = this.createButton(
        this.t('settings.update.card.buttons.retry'),
        'update-btn update-btn-primary',
        async () => {
          retryBtn.disabled = true;
          retryBtn.innerHTML = `<span class="update-loading"></span>重试中...`;
          try {
            const result = await ipcRenderer.invoke('retry-update-download');
            if (result.success) {
              this.titleEl.textContent = this.t('settings.update.card.downloading');
              this.progressBarEl.style.display = 'block';
              this.progressTextEl.style.display = 'block';
              this.errorDetailsEl.style.display = 'none';
            } else {
              this.showUpdateError(result.error, {
                isNetworkError: result.isNetworkError,
                canRetry: true,
                retryCount: 0
              });
            }
          } catch (e) {
            this.showUpdateError(e.message, { canRetry: true });
          }
        }
      );
      this.actionContainerEl.appendChild(retryBtn);
    }

    const closeBtn = this.createButton(
      this.t('settings.update.card.buttons.close'),
      'update-btn update-btn-secondary',
      () => this.hideCard()
    );
    this.actionContainerEl.appendChild(closeBtn);
  }

  showRetryProgress(retryInfo) {
    this.ensureUpdateCardContainer();
    this.cardEl.className = 'update-card retrying';
    this.titleEl.textContent = `正在重试下载... (${retryInfo.retryCount}/${retryInfo.maxRetries})`;
    this.progressBarEl.style.display = 'none';
    this.progressTextEl.style.display = 'none';

    if (this.errorDetailsEl) {
      this.errorDetailsEl.style.display = 'none';
    }

    this.actionContainerEl.innerHTML = '<div class="retry-spinner">重试中...</div>';
  }

  showRetryFailed(failInfo) {
    this.showUpdateError(failInfo.error, {
      isNetworkError: true,
      canRetry: false,
      retryCount: failInfo.retryCount
    });
  }

  createButton(text, className, onClick) {
    const btn = document.createElement('button');
    btn.textContent = text;
    btn.className = className;
    btn.onclick = onClick;
    return btn;
  }

  hideCard() {
    if (this.cardEl) {
      this.cardEl.remove();
      this.cardEl = null;
    }
  }

  updateCardTexts() {
    if (!this.cardEl) return;
    
    // 更新关闭按钮标题
    const closeBtn = this.cardEl.querySelector('.update-card-close');
    if (closeBtn) {
      closeBtn.title = this.t('settings.update.card.buttons.close');
    }
    
    // 更新按钮文本
    const buttons = this.cardEl.querySelectorAll('.update-btn');
    buttons.forEach(btn => {
      // 这里可以根据按钮的类名或数据属性来更新文本
      // 由于按钮是动态创建的，实际更新会在下次显示时生效
    });
  }

  t(key, params = {}) {
    return this.getNestedValue(this.i18nTexts, key, params) || key;
  }

  getNestedValue(obj, key, params = {}) {
    if (!obj || !key) return undefined;
    
    let value = key.split('.').reduce((current, keyPart) => {
      return current && current[keyPart] !== undefined ? current[keyPart] : undefined;
    }, obj);
    
    if (typeof value === 'string' && Object.keys(params).length > 0) {
      value = value.replace(/\{(\w+)\}/g, (match, paramKey) => {
        return params[paramKey] !== undefined ? params[paramKey] : match;
      });
    }
    
    return value;
  }

  formatBytes(bytes) {
    if (!Number.isFinite(bytes)) return '0 B';
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let i = 0;
    let num = bytes;
    while (num >= 1024 && i < units.length - 1) {
      num /= 1024;
      i++;
    }
    return `${num.toFixed(num < 10 && i > 0 ? 1 : 0)} ${units[i]}`;
  }

  // 公共方法：刷新设置（当设置页面更改自动更新选项时调用）
  async refreshSettings() {
    try {
      this.autoUpdateEnabled = await ipcRenderer.invoke('get-setting', 'autoUpdate');
      const skipped = await ipcRenderer.invoke('get-setting', 'skippedVersions') || [];
      this.skippedVersions = new Set(skipped);
      
      // 如果开启了自动更新，隐藏卡片
      if (this.autoUpdateEnabled !== false) {
        this.hideCard();
      }
    } catch (e) {
      console.warn('刷新更新设置失败:', e);
    }
  }
}

// 导出单例实例
let updateCardInstance = null;

function getUpdateCard() {
  if (!updateCardInstance) {
    updateCardInstance = new UpdateCard();
  }
  return updateCardInstance;
}

module.exports = { getUpdateCard, UpdateCard };
