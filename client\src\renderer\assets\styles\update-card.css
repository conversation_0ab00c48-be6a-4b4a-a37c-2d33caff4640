/* 更新提示卡片样式 - 支持明暗主题 */

:root {
  /* 明亮主题 */
  --update-card-bg: rgba(255, 255, 255, 0.95);
  --update-card-text: #333;
  --update-card-border: rgba(0, 0, 0, 0.1);
  --update-card-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  --update-card-close-color: #666;
  --update-card-close-hover: #333;
  --update-progress-bg: rgba(0, 0, 0, 0.1);
  --update-progress-fill: #4caf50;
  --update-btn-primary-bg: #4caf50;
  --update-btn-primary-text: #fff;
  --update-btn-primary-hover: #45a049;
  --update-btn-secondary-bg: transparent;
  --update-btn-secondary-text: #666;
  --update-btn-secondary-border: rgba(0, 0, 0, 0.2);
  --update-btn-secondary-hover: rgba(0, 0, 0, 0.05);
  --update-btn-skip-bg: #ff9800;
  --update-btn-skip-text: #fff;
  --update-btn-skip-hover: #f57c00;
}

[data-theme="dark"] {
  /* 暗黑主题 */
  --update-card-bg: rgba(30, 30, 30, 0.95);
  --update-card-text: #e0e0e0;
  --update-card-border: rgba(255, 255, 255, 0.1);
  --update-card-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  --update-card-close-color: #bbb;
  --update-card-close-hover: #fff;
  --update-progress-bg: rgba(255, 255, 255, 0.2);
  --update-progress-fill: #66bb6a;
  --update-btn-primary-bg: #66bb6a;
  --update-btn-primary-text: #fff;
  --update-btn-primary-hover: #5cb85c;
  --update-btn-secondary-bg: transparent;
  --update-btn-secondary-text: #bbb;
  --update-btn-secondary-border: rgba(255, 255, 255, 0.3);
  --update-btn-secondary-hover: rgba(255, 255, 255, 0.1);
  --update-btn-skip-bg: #ff9800;
  --update-btn-skip-text: #fff;
  --update-btn-skip-hover: #f57c00;
}

[data-theme="auto"] {
  /* 自动主题 - 根据系统偏好 */
  --update-card-bg: rgba(255, 255, 255, 0.95);
  --update-card-text: #333;
  --update-card-border: rgba(0, 0, 0, 0.1);
  --update-card-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  --update-card-close-color: #666;
  --update-card-close-hover: #333;
  --update-progress-bg: rgba(0, 0, 0, 0.1);
  --update-progress-fill: #4caf50;
  --update-btn-primary-bg: #4caf50;
  --update-btn-primary-text: #fff;
  --update-btn-primary-hover: #45a049;
  --update-btn-secondary-bg: transparent;
  --update-btn-secondary-text: #666;
  --update-btn-secondary-border: rgba(0, 0, 0, 0.2);
  --update-btn-secondary-hover: rgba(0, 0, 0, 0.05);
  --update-btn-skip-bg: #ff9800;
  --update-btn-skip-text: #fff;
  --update-btn-skip-hover: #f57c00;
}

@media (prefers-color-scheme: dark) {
  [data-theme="auto"] {
    --update-card-bg: rgba(30, 30, 30, 0.95);
    --update-card-text: #e0e0e0;
    --update-card-border: rgba(255, 255, 255, 0.1);
    --update-card-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
    --update-card-close-color: #bbb;
    --update-card-close-hover: #fff;
    --update-progress-bg: rgba(255, 255, 255, 0.2);
    --update-progress-fill: #66bb6a;
    --update-btn-primary-bg: #66bb6a;
    --update-btn-primary-text: #fff;
    --update-btn-primary-hover: #5cb85c;
    --update-btn-secondary-bg: transparent;
    --update-btn-secondary-text: #bbb;
    --update-btn-secondary-border: rgba(255, 255, 255, 0.3);
    --update-btn-secondary-hover: rgba(255, 255, 255, 0.1);
  }
}

/* 更新卡片主容器 */
.update-card {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  background: var(--update-card-bg);
  color: var(--update-card-text);
  padding: 16px;
  border-radius: 12px;
  min-width: 300px;
  max-width: 360px;
  box-shadow: var(--update-card-shadow);
  border: 1px solid var(--update-card-border);
  backdrop-filter: blur(10px);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  animation: slideInFromRight 0.3s ease-out;
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 关闭按钮 */
.update-card-close {
  position: absolute;
  top: 8px;
  right: 8px;
  background: transparent;
  border: none;
  color: var(--update-card-close-color);
  font-size: 14px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.update-card-close:hover {
  color: var(--update-card-close-hover);
}

/* 标题 */
.update-card-title {
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 14px;
  padding-right: 20px;
}

/* 进度条容器 */
.update-progress-bar {
  height: 8px;
  background: var(--update-progress-bg);
  border-radius: 6px;
  overflow: hidden;
  margin-top: 8px;
  display: none;
}

.update-progress-inner {
  height: 100%;
  width: 0%;
  background: var(--update-progress-fill);
  transition: width 0.2s ease;
  border-radius: 6px;
}

/* 进度文本 */
.update-progress-text {
  margin-top: 6px;
  font-size: 12px;
  opacity: 0.8;
  display: none;
}

/* 按钮容器 */
.update-actions {
  margin-top: 12px;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  flex-wrap: wrap;
}

/* 按钮基础样式 */
.update-btn {
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
  min-width: 60px;
  text-align: center;
}

.update-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 主要按钮（立即更新/立即安装） */
.update-btn-primary {
  background: var(--update-btn-primary-bg);
  color: var(--update-btn-primary-text);
}

.update-btn-primary:hover:not(:disabled) {
  background: var(--update-btn-primary-hover);
  transform: translateY(-1px);
}

/* 次要按钮（稍后/关闭） */
.update-btn-secondary {
  background: var(--update-btn-secondary-bg);
  color: var(--update-btn-secondary-text);
  border: 1px solid var(--update-btn-secondary-border);
}

.update-btn-secondary:hover:not(:disabled) {
  background: var(--update-btn-secondary-hover);
}

/* 跳过按钮 */
.update-btn-skip {
  background: var(--update-btn-skip-bg);
  color: var(--update-btn-skip-text);
}

.update-btn-skip:hover:not(:disabled) {
  background: var(--update-btn-skip-hover);
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .update-card {
    left: 10px;
    right: 10px;
    top: 10px;
    min-width: auto;
    max-width: none;
  }
  
  .update-actions {
    flex-direction: column;
  }
  
  .update-btn {
    width: 100%;
  }
}

/* 加载动画 */
.update-loading {
  display: inline-block;
  width: 12px;
  height: 12px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 6px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.update-card.error .update-card-title {
  color: #f44336;
}

.update-card.error .update-progress-bar {
  display: none;
}

/* 成功状态 */
.update-card.success .update-card-title {
  color: var(--update-progress-fill);
}

/* 重试状态 */
.update-card.retrying .update-card-title {
  color: #ff9800;
}

/* 错误详情 */
.update-error-details {
  margin: 8px 0;
  padding: 8px;
  background: rgba(244, 67, 54, 0.1);
  border-radius: 6px;
  border-left: 3px solid #f44336;
}

.update-error-details .error-message {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #f44336;
  font-weight: 500;
}

.update-error-details .error-hint {
  margin: 4px 0;
  font-size: 11px;
  opacity: 0.8;
}

.update-error-details .retry-info {
  margin: 4px 0 0 0;
  font-size: 11px;
  opacity: 0.7;
  font-style: italic;
}

/* 重试加载器 */
.retry-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  font-size: 12px;
  color: #ff9800;
}

.retry-spinner::before {
  content: '';
  display: inline-block;
  width: 12px;
  height: 12px;
  border: 2px solid transparent;
  border-top: 2px solid #ff9800;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 6px;
}

/* 重试按钮特殊样式 */
.update-btn.retry-btn {
  background: #ff9800;
  color: #fff;
}

.update-btn.retry-btn:hover:not(:disabled) {
  background: #f57c00;
  transform: translateY(-1px);
}

/* 暗黑主题下的错误详情 */
[data-theme="dark"] .update-error-details {
  background: rgba(244, 67, 54, 0.2);
}

[data-theme="auto"] .update-error-details {
  background: rgba(244, 67, 54, 0.1);
}

@media (prefers-color-scheme: dark) {
  [data-theme="auto"] .update-error-details {
    background: rgba(244, 67, 54, 0.2);
  }
}
