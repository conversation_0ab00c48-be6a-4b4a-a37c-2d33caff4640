#!/usr/bin/env node

/**
 * Cosign 安装脚本
 * 自动检测操作系统并安装 cosign 工具
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const https = require('https');
const os = require('os');

const COSIGN_VERSION = 'latest';
const GITHUB_API_URL = 'https://api.github.com/repos/sigstore/cosign/releases/latest';

/**
 * 检查 cosign 是否已安装
 */
function checkCosignInstalled() {
  return new Promise((resolve) => {
    const cosign = spawn('cosign', ['version'], { stdio: 'pipe' });
    cosign.on('close', (code) => {
      resolve(code === 0);
    });
    cosign.on('error', () => {
      resolve(false);
    });
  });
}

/**
 * 获取最新的 cosign 版本信息
 */
function getLatestVersion() {
  return new Promise((resolve, reject) => {
    https.get(GITHUB_API_URL, {
      headers: {
        'User-Agent': 'AiTools-Installer'
      }
    }, (response) => {
      let data = '';
      
      response.on('data', (chunk) => {
        data += chunk;
      });
      
      response.on('end', () => {
        try {
          const release = JSON.parse(data);
          resolve({
            version: release.tag_name,
            assets: release.assets
          });
        } catch (error) {
          reject(error);
        }
      });
    }).on('error', (error) => {
      reject(error);
    });
  });
}

/**
 * 下载文件
 */
function downloadFile(url, outputPath) {
  return new Promise((resolve, reject) => {
    console.log(`下载: ${url}`);
    
    const file = fs.createWriteStream(outputPath);
    
    https.get(url, (response) => {
      if (response.statusCode === 302 || response.statusCode === 301) {
        // 处理重定向
        return downloadFile(response.headers.location, outputPath)
          .then(resolve)
          .catch(reject);
      }
      
      if (response.statusCode !== 200) {
        reject(new Error(`下载失败: HTTP ${response.statusCode}`));
        return;
      }
      
      const totalSize = parseInt(response.headers['content-length'], 10);
      let downloadedSize = 0;
      
      response.on('data', (chunk) => {
        downloadedSize += chunk.length;
        if (totalSize) {
          const progress = ((downloadedSize / totalSize) * 100).toFixed(1);
          process.stdout.write(`\r下载进度: ${progress}%`);
        }
      });
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log('\n下载完成');
        resolve();
      });
    }).on('error', (error) => {
      fs.unlink(outputPath, () => {}); // 删除不完整的文件
      reject(error);
    });
  });
}

/**
 * 执行命令
 */
function executeCommand(command) {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        reject(error);
      } else {
        resolve({ stdout, stderr });
      }
    });
  });
}

/**
 * Windows 安装
 */
async function installWindows(version, assets) {
  console.log('检测到 Windows 系统');
  
  const asset = assets.find(a => a.name.includes('windows-amd64.exe'));
  if (!asset) {
    throw new Error('找不到 Windows 版本的 cosign');
  }
  
  const tempDir = os.tmpdir();
  const cosignPath = path.join(tempDir, 'cosign.exe');
  
  await downloadFile(asset.browser_download_url, cosignPath);
  
  // 移动到系统路径
  const systemPath = path.join(process.env.PROGRAMFILES || 'C:\\Program Files', 'cosign');
  
  try {
    if (!fs.existsSync(systemPath)) {
      fs.mkdirSync(systemPath, { recursive: true });
    }
    
    const finalPath = path.join(systemPath, 'cosign.exe');
    fs.copyFileSync(cosignPath, finalPath);
    fs.unlinkSync(cosignPath);
    
    console.log(`✅ cosign 已安装到: ${finalPath}`);
    console.log('请将以下路径添加到系统 PATH 环境变量:');
    console.log(systemPath);
    
  } catch (error) {
    // 如果没有管理员权限，安装到用户目录
    const userPath = path.join(os.homedir(), '.local', 'bin');
    if (!fs.existsSync(userPath)) {
      fs.mkdirSync(userPath, { recursive: true });
    }
    
    const finalPath = path.join(userPath, 'cosign.exe');
    fs.copyFileSync(cosignPath, finalPath);
    fs.unlinkSync(cosignPath);
    
    console.log(`✅ cosign 已安装到用户目录: ${finalPath}`);
    console.log('请将以下路径添加到用户 PATH 环境变量:');
    console.log(userPath);
  }
}

/**
 * macOS 安装
 */
async function installMacOS(version, assets) {
  console.log('检测到 macOS 系统');
  
  // 首先尝试使用 Homebrew
  try {
    console.log('尝试使用 Homebrew 安装...');
    await executeCommand('brew install cosign');
    console.log('✅ 通过 Homebrew 安装成功');
    return;
  } catch (error) {
    console.log('Homebrew 不可用，使用手动安装...');
  }
  
  const asset = assets.find(a => a.name.includes('darwin-amd64') || a.name.includes('darwin-universal'));
  if (!asset) {
    throw new Error('找不到 macOS 版本的 cosign');
  }
  
  const tempDir = os.tmpdir();
  const cosignPath = path.join(tempDir, 'cosign');
  
  await downloadFile(asset.browser_download_url, cosignPath);
  
  // 设置执行权限
  fs.chmodSync(cosignPath, '755');
  
  // 移动到 /usr/local/bin
  try {
    await executeCommand(`sudo mv ${cosignPath} /usr/local/bin/cosign`);
    console.log('✅ cosign 已安装到 /usr/local/bin/cosign');
  } catch (error) {
    // 如果没有 sudo 权限，安装到用户目录
    const userBin = path.join(os.homedir(), '.local', 'bin');
    if (!fs.existsSync(userBin)) {
      fs.mkdirSync(userBin, { recursive: true });
    }
    
    fs.copyFileSync(cosignPath, path.join(userBin, 'cosign'));
    fs.unlinkSync(cosignPath);
    
    console.log(`✅ cosign 已安装到用户目录: ${userBin}/cosign`);
    console.log('请确保 ~/.local/bin 在您的 PATH 中');
  }
}

/**
 * Linux 安装
 */
async function installLinux(version, assets) {
  console.log('检测到 Linux 系统');
  
  const asset = assets.find(a => a.name.includes('linux-amd64') && !a.name.includes('.sig'));
  if (!asset) {
    throw new Error('找不到 Linux 版本的 cosign');
  }
  
  const tempDir = os.tmpdir();
  const cosignPath = path.join(tempDir, 'cosign');
  
  await downloadFile(asset.browser_download_url, cosignPath);
  
  // 设置执行权限
  fs.chmodSync(cosignPath, '755');
  
  // 移动到 /usr/local/bin
  try {
    await executeCommand(`sudo mv ${cosignPath} /usr/local/bin/cosign`);
    console.log('✅ cosign 已安装到 /usr/local/bin/cosign');
  } catch (error) {
    // 如果没有 sudo 权限，安装到用户目录
    const userBin = path.join(os.homedir(), '.local', 'bin');
    if (!fs.existsSync(userBin)) {
      fs.mkdirSync(userBin, { recursive: true });
    }
    
    fs.copyFileSync(cosignPath, path.join(userBin, 'cosign'));
    fs.unlinkSync(cosignPath);
    
    console.log(`✅ cosign 已安装到用户目录: ${userBin}/cosign`);
    console.log('请确保 ~/.local/bin 在您的 PATH 中');
  }
}

/**
 * 主安装函数
 */
async function main() {
  console.log('🔍 检查 cosign 安装状态...');
  
  const isInstalled = await checkCosignInstalled();
  if (isInstalled) {
    console.log('✅ cosign 已经安装');
    
    // 显示版本信息
    try {
      const { stdout } = await executeCommand('cosign version');
      console.log('版本信息:');
      console.log(stdout);
    } catch (error) {
      console.log('无法获取版本信息');
    }
    
    return;
  }
  
  console.log('📦 cosign 未安装，开始安装...');
  
  try {
    console.log('🔍 获取最新版本信息...');
    const { version, assets } = await getLatestVersion();
    console.log(`最新版本: ${version}`);
    
    const platform = os.platform();
    
    switch (platform) {
      case 'win32':
        await installWindows(version, assets);
        break;
      case 'darwin':
        await installMacOS(version, assets);
        break;
      case 'linux':
        await installLinux(version, assets);
        break;
      default:
        throw new Error(`不支持的操作系统: ${platform}`);
    }
    
    console.log('\n🎉 安装完成！');
    console.log('请重新启动终端或重新加载 PATH 环境变量');
    console.log('然后运行 "cosign version" 验证安装');
    
  } catch (error) {
    console.error('❌ 安装失败:', error.message);
    console.log('\n手动安装说明:');
    console.log('1. 访问 https://github.com/sigstore/cosign/releases');
    console.log('2. 下载适合您操作系统的版本');
    console.log('3. 将可执行文件放到 PATH 目录中');
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  checkCosignInstalled,
  getLatestVersion
};
