#!/usr/bin/env node

/**
 * 签名功能测试脚本
 * 用于测试 Sigstore 签名和验证功能
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { checkCosignInstalled } = require('./install-cosign');
const { verifyDirectory } = require('./verify-signature');

/**
 * 创建测试文件
 */
function createTestFile(filePath, content = 'Test file for signing') {
  fs.writeFileSync(filePath, content);
  console.log(`✅ 创建测试文件: ${filePath}`);
}

/**
 * 创建模拟签名文件
 */
function createMockSignatureFiles(filePath) {
  const sigPath = filePath + '.sig';
  const crtPath = filePath + '.crt';
  
  // 创建模拟签名文件（实际环境中由 cosign 生成）
  fs.writeFileSync(sigPath, 'mock-signature-data');
  fs.writeFileSync(crtPath, 'mock-certificate-data');
  
  console.log(`✅ 创建模拟签名文件: ${path.basename(sigPath)}, ${path.basename(crtPath)}`);
}

/**
 * 清理测试文件
 */
function cleanupTestFiles(directory) {
  const testFiles = [
    'test-app.exe',
    'test-app.exe.sig',
    'test-app.exe.crt',
    'test-app.dmg',
    'test-app.dmg.sig',
    'test-app.dmg.crt',
    'test-app.AppImage',
    'test-app.AppImage.sig',
    'test-app.AppImage.crt'
  ];
  
  testFiles.forEach(file => {
    const filePath = path.join(directory, file);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
  });
  
  console.log('🧹 清理测试文件完成');
}

/**
 * 测试签名验证功能
 */
async function testSignatureVerification() {
  console.log('🧪 开始测试签名验证功能...\n');
  
  const testDir = path.join(__dirname, '..', 'test-signing');
  
  // 创建测试目录
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
  }
  
  try {
    // 检查 cosign 是否可用
    console.log('1. 检查 cosign 工具...');
    const cosignAvailable = await checkCosignInstalled();
    
    if (cosignAvailable) {
      console.log('✅ cosign 工具可用');
    } else {
      console.log('⚠️  cosign 工具不可用，将跳过实际验证');
      console.log('   可以运行 "npm run install-cosign" 安装');
    }
    
    // 创建测试文件
    console.log('\n2. 创建测试文件...');
    const testFiles = [
      { name: 'test-app.exe', platform: 'Windows' },
      { name: 'test-app.dmg', platform: 'macOS' },
      { name: 'test-app.AppImage', platform: 'Linux' }
    ];
    
    testFiles.forEach(({ name, platform }) => {
      const filePath = path.join(testDir, name);
      const content = `Test application for ${platform}\nGenerated at: ${new Date().toISOString()}`;
      createTestFile(filePath, content);
      
      // 创建模拟签名文件
      if (!cosignAvailable) {
        createMockSignatureFiles(filePath);
      }
    });
    
    // 测试验证功能
    console.log('\n3. 测试验证功能...');
    
    if (cosignAvailable) {
      console.log('⚠️  注意: 由于测试文件没有真实的 Sigstore 签名，验证将会失败');
      console.log('   这是正常的，说明验证功能工作正常');
    }
    
    try {
      const results = await verifyDirectory(testDir);
      
      console.log('\n📊 验证结果:');
      console.log(`✅ 验证成功: ${results.verified.length} 个文件`);
      console.log(`❌ 验证失败: ${results.failed.length} 个文件`);
      console.log(`⏭️  跳过验证: ${results.skipped.length} 个文件`);
      
      if (results.verified.length > 0) {
        console.log('\n✅ 验证成功的文件:');
        results.verified.forEach(item => {
          console.log(`  - ${item.file}`);
        });
      }
      
      if (results.failed.length > 0) {
        console.log('\n❌ 验证失败的文件:');
        results.failed.forEach(item => {
          console.log(`  - ${item.file}: ${item.error}`);
        });
      }
      
      if (results.skipped.length > 0) {
        console.log('\n⏭️  跳过的文件:');
        results.skipped.forEach(item => {
          console.log(`  - ${item.file}: ${item.reason}`);
        });
      }
      
    } catch (error) {
      console.error('❌ 验证过程中出错:', error.message);
    }
    
    // 测试文件哈希计算
    console.log('\n4. 测试文件哈希计算...');
    const testFile = path.join(testDir, 'test-app.exe');
    if (fs.existsSync(testFile)) {
      const hash = crypto.createHash('sha256');
      const content = fs.readFileSync(testFile);
      hash.update(content);
      const fileHash = hash.digest('hex');
      console.log(`✅ 文件哈希计算成功: ${fileHash.substring(0, 16)}...`);
    }
    
    console.log('\n🎉 签名功能测试完成！');
    
    if (!cosignAvailable) {
      console.log('\n💡 提示:');
      console.log('   - 安装 cosign 工具以启用完整的签名验证功能');
      console.log('   - 运行 "npm run install-cosign" 自动安装');
      console.log('   - 或访问 https://docs.sigstore.dev/cosign/installation/ 手动安装');
    }
    
  } finally {
    // 清理测试文件
    console.log('\n5. 清理测试文件...');
    cleanupTestFiles(testDir);
    
    // 删除测试目录
    if (fs.existsSync(testDir)) {
      fs.rmdirSync(testDir);
    }
  }
}

/**
 * 显示签名配置信息
 */
function showSigningInfo() {
  console.log('📋 Sigstore 签名配置信息:\n');
  
  console.log('🔧 GitHub Actions 配置:');
  console.log('   - 权限: id-token: write (已配置)');
  console.log('   - 环境变量: COSIGN_EXPERIMENTAL=1');
  console.log('   - 签名工具: cosign');
  
  console.log('\n📦 支持的文件类型:');
  console.log('   - Windows: .exe, .msi');
  console.log('   - macOS: .dmg, .zip');
  console.log('   - Linux: .AppImage, .deb');
  
  console.log('\n🔍 验证功能:');
  console.log('   - 自动验证: 更新时自动验证签名');
  console.log('   - 手动验证: npm run verify-dist');
  console.log('   - 单文件验证: npm run verify-signature <file>');
  
  console.log('\n🛠️  可用命令:');
  console.log('   - npm run install-cosign    # 安装 cosign 工具');
  console.log('   - npm run verify-dist       # 验证 dist 目录');
  console.log('   - npm run verify-signature  # 验证单个文件');
  
  console.log('\n📚 文档:');
  console.log('   - 详细说明: SIGSTORE_SIGNING.md');
  console.log('   - Sigstore 官网: https://www.sigstore.dev/');
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--info')) {
    showSigningInfo();
    return;
  }
  
  console.log('🔒 Sigstore 签名功能测试\n');
  
  try {
    await testSignatureVerification();
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  testSignatureVerification,
  showSigningInfo
};
