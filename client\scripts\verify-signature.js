#!/usr/bin/env node

/**
 * Sigstore 签名验证脚本
 * 用于验证下载的更新文件的 Sigstore 签名
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

/**
 * 检查 cosign 是否已安装
 */
function checkCosignInstalled() {
  return new Promise((resolve) => {
    const cosign = spawn('cosign', ['version'], { stdio: 'pipe' });
    cosign.on('close', (code) => {
      resolve(code === 0);
    });
    cosign.on('error', () => {
      resolve(false);
    });
  });
}

/**
 * 验证文件的 Sigstore 签名
 * @param {string} filePath - 要验证的文件路径
 * @param {string} signaturePath - 签名文件路径
 * @param {string} certificatePath - 证书文件路径
 * @returns {Promise<boolean>} 验证结果
 */
function verifySignature(filePath, signaturePath, certificatePath) {
  return new Promise((resolve, reject) => {
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      reject(new Error(`文件不存在: ${filePath}`));
      return;
    }
    
    if (!fs.existsSync(signaturePath)) {
      reject(new Error(`签名文件不存在: ${signaturePath}`));
      return;
    }
    
    if (!fs.existsSync(certificatePath)) {
      reject(new Error(`证书文件不存在: ${certificatePath}`));
      return;
    }

    console.log(`🔍 验证文件签名: ${path.basename(filePath)}`);
    
    // 使用 cosign 验证签名
    const cosign = spawn('cosign', [
      'verify-blob',
      '--signature', signaturePath,
      '--certificate', certificatePath,
      '--certificate-identity-regexp', '.*',
      '--certificate-oidc-issuer-regexp', '.*',
      filePath
    ], {
      stdio: 'pipe',
      env: {
        ...process.env,
        COSIGN_EXPERIMENTAL: '1'
      }
    });

    let stdout = '';
    let stderr = '';

    cosign.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    cosign.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    cosign.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ 签名验证成功: ${path.basename(filePath)}`);
        resolve(true);
      } else {
        console.error(`❌ 签名验证失败: ${path.basename(filePath)}`);
        console.error('错误输出:', stderr);
        resolve(false);
      }
    });

    cosign.on('error', (error) => {
      console.error(`❌ 执行 cosign 时出错:`, error.message);
      reject(error);
    });
  });
}

/**
 * 计算文件的 SHA256 哈希值
 * @param {string} filePath - 文件路径
 * @returns {Promise<string>} 文件哈希值
 */
function calculateFileHash(filePath) {
  return new Promise((resolve, reject) => {
    const hash = crypto.createHash('sha256');
    const stream = fs.createReadStream(filePath);
    
    stream.on('data', (data) => {
      hash.update(data);
    });
    
    stream.on('end', () => {
      resolve(hash.digest('hex'));
    });
    
    stream.on('error', (error) => {
      reject(error);
    });
  });
}

/**
 * 验证目录中所有文件的签名
 * @param {string} directory - 目录路径
 * @returns {Promise<Object>} 验证结果
 */
async function verifyDirectory(directory) {
  const results = {
    verified: [],
    failed: [],
    skipped: []
  };

  if (!fs.existsSync(directory)) {
    throw new Error(`目录不存在: ${directory}`);
  }

  const files = fs.readdirSync(directory);
  const executableFiles = files.filter(file => {
    const ext = path.extname(file).toLowerCase();
    return ['.exe', '.msi', '.dmg', '.zip', '.appimage', '.deb'].includes(ext);
  });

  for (const file of executableFiles) {
    const filePath = path.join(directory, file);
    const signaturePath = filePath + '.sig';
    const certificatePath = filePath + '.crt';

    if (fs.existsSync(signaturePath) && fs.existsSync(certificatePath)) {
      try {
        const isValid = await verifySignature(filePath, signaturePath, certificatePath);
        const fileHash = await calculateFileHash(filePath);
        
        if (isValid) {
          results.verified.push({
            file: file,
            hash: fileHash,
            signature: path.basename(signaturePath),
            certificate: path.basename(certificatePath)
          });
        } else {
          results.failed.push({
            file: file,
            hash: fileHash,
            error: '签名验证失败'
          });
        }
      } catch (error) {
        results.failed.push({
          file: file,
          error: error.message
        });
      }
    } else {
      results.skipped.push({
        file: file,
        reason: '缺少签名或证书文件'
      });
    }
  }

  return results;
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('用法:');
    console.log('  node verify-signature.js <file-path>           # 验证单个文件');
    console.log('  node verify-signature.js --dir <directory>    # 验证目录中的所有文件');
    process.exit(1);
  }

  // 检查 cosign 是否已安装
  const cosignInstalled = await checkCosignInstalled();
  if (!cosignInstalled) {
    console.error('❌ cosign 未安装或不在 PATH 中');
    console.error('请安装 cosign: https://docs.sigstore.dev/cosign/installation/');
    process.exit(1);
  }

  try {
    if (args[0] === '--dir') {
      // 验证目录
      const directory = args[1];
      if (!directory) {
        console.error('❌ 请指定目录路径');
        process.exit(1);
      }

      console.log(`🔍 验证目录中的所有文件: ${directory}`);
      const results = await verifyDirectory(directory);
      
      console.log('\n📊 验证结果:');
      console.log(`✅ 验证成功: ${results.verified.length} 个文件`);
      console.log(`❌ 验证失败: ${results.failed.length} 个文件`);
      console.log(`⏭️  跳过验证: ${results.skipped.length} 个文件`);

      if (results.verified.length > 0) {
        console.log('\n✅ 验证成功的文件:');
        results.verified.forEach(item => {
          console.log(`  - ${item.file} (SHA256: ${item.hash.substring(0, 16)}...)`);
        });
      }

      if (results.failed.length > 0) {
        console.log('\n❌ 验证失败的文件:');
        results.failed.forEach(item => {
          console.log(`  - ${item.file}: ${item.error}`);
        });
      }

      if (results.skipped.length > 0) {
        console.log('\n⏭️  跳过的文件:');
        results.skipped.forEach(item => {
          console.log(`  - ${item.file}: ${item.reason}`);
        });
      }

      // 如果有验证失败的文件，退出码为 1
      if (results.failed.length > 0) {
        process.exit(1);
      }
    } else {
      // 验证单个文件
      const filePath = args[0];
      const signaturePath = filePath + '.sig';
      const certificatePath = filePath + '.crt';

      const isValid = await verifySignature(filePath, signaturePath, certificatePath);
      
      if (isValid) {
        const fileHash = await calculateFileHash(filePath);
        console.log(`📋 文件哈希: ${fileHash}`);
        console.log('🎉 文件签名验证成功！');
        process.exit(0);
      } else {
        console.log('💥 文件签名验证失败！');
        process.exit(1);
      }
    }
  } catch (error) {
    console.error('❌ 验证过程中出错:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  verifySignature,
  verifyDirectory,
  calculateFileHash,
  checkCosignInstalled
};
