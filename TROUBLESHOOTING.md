# GitHub Actions 构建故障排除指南

## 常见构建失败原因及解决方案

### 1. 依赖安装失败

**症状**: `npm install` 步骤失败
**原因**: 
- 网络连接问题
- 依赖版本冲突
- Node.js 版本不兼容

**解决方案**:
```yaml
- name: Install client dependencies
  run: |
    cd client
    npm ci --legacy-peer-deps
  # 或者使用
  # npm install --legacy-peer-deps --no-audit --no-fund
```

### 2. Electron Builder 构建失败

**症状**: `npm run build` 步骤失败
**原因**:
- 图标文件缺失或格式错误
- 构建配置错误
- 内存不足

**解决方案**:
1. 检查图标文件:
   ```bash
   # 在本地运行诊断
   cd client
   npm run diagnose
   ```

2. 简化构建配置:
   ```json
   {
     "win": {
       "target": [{"target": "nsis", "arch": ["x64"]}]
     },
     "mac": {
       "target": [{"target": "dmg", "arch": ["x64"]}]
     },
     "linux": {
       "target": [{"target": "AppImage", "arch": ["x64"]}]
     }
   }
   ```

### 3. Sigstore 签名失败

**症状**: 签名步骤失败但不影响构建
**原因**:
- cosign 安装失败
- 网络连接问题
- 权限问题

**解决方案**:
签名步骤已设置为 `continue-on-error: true`，不会阻止构建完成。

### 4. 文件上传失败

**症状**: 构建成功但 Release 创建失败
**原因**:
- 文件路径错误
- 权限不足
- 文件不存在

**解决方案**:
检查 artifacts 上传配置:
```yaml
- name: Upload artifacts
  uses: actions/upload-artifact@v4
  with:
    name: build-artifacts
    path: client/dist/
    if-no-files-found: warn
```

## 调试步骤

### 1. 本地测试

在推送到 GitHub 之前，先在本地测试:

```bash
# 进入客户端目录
cd client

# 运行诊断
npm run diagnose

# 安装依赖
npm install --legacy-peer-deps

# 测试构建
npm run build:win    # Windows
npm run build:mac    # macOS (仅在 macOS 上)
npm run build:linux  # Linux
```

### 2. 查看 GitHub Actions 日志

1. 进入 GitHub 仓库
2. 点击 "Actions" 标签
3. 选择失败的工作流
4. 查看详细日志

### 3. 使用简化配置

如果构建持续失败，可以使用简化的配置:

```bash
# 使用简化的构建配置
cp .github/workflows/build-simple.yml .github/workflows/build.yml
```

### 4. 手动触发构建

可以手动触发构建来测试:

1. 进入 GitHub 仓库的 Actions 页面
2. 选择 "Build and Release" 工作流
3. 点击 "Run workflow"
4. 选择分支并运行

## 环境要求

### Node.js 版本
- 推荐: Node.js 18.x
- 最低: Node.js 16.x

### 系统要求
- Windows: Windows Server 2019 或更新
- macOS: macOS 11 或更新
- Linux: Ubuntu 20.04 或更新

### 内存要求
- 最低: 4GB RAM
- 推荐: 8GB RAM

## 常用命令

### 本地调试
```bash
# 诊断构建环境
npm run diagnose

# 清理并重新安装依赖
rm -rf node_modules package-lock.json
npm install --legacy-peer-deps

# 测试签名功能
npm run test-signing

# 查看签名配置信息
npm run signing-info
```

### GitHub Actions 调试
```bash
# 创建新的 tag 触发构建
git tag v1.0.37
git push origin v1.0.37

# 删除 tag (如果需要重新构建)
git tag -d v1.0.37
git push origin :refs/tags/v1.0.37
```

## 配置文件说明

### 主要配置文件
- `.github/workflows/build.yml` - 主构建配置
- `.github/workflows/build-simple.yml` - 简化构建配置
- `client/package.json` - 应用配置和依赖
- `client/electron-builder-debug.json` - 调试用构建配置

### 关键设置
```json
{
  "build": {
    "directories": {
      "output": "dist"
    },
    "files": [
      "src/**/*",
      "node_modules/**/*",
      "package.json"
    ]
  }
}
```

## 获取帮助

### 查看日志
1. GitHub Actions 日志
2. 本地构建日志
3. electron-builder 详细输出

### 社区资源
- [Electron Builder 文档](https://www.electron.build/)
- [GitHub Actions 文档](https://docs.github.com/en/actions)
- [Sigstore 文档](https://docs.sigstore.dev/)

### 联系支持
如果问题持续存在，请:
1. 收集完整的错误日志
2. 提供系统环境信息
3. 描述重现步骤
4. 创建 GitHub Issue
