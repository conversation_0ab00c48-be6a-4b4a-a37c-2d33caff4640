name: Build and Release

on:
  push:
    tags: [ 'v*' ]
  workflow_dispatch:  # 允许手动触发

permissions:
  contents: write
  packages: read
  id-token: write  # 为 Sigstore 签名添加权限

jobs:
  build:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install client dependencies
      run: |
        cd client
        npm install --legacy-peer-deps

    - name: Get current date
      id: date
      run: echo "date=$(date +'%Y-%m-%d')" >> $GITHUB_OUTPUT
      shell: bash

    # 安装 Sigstore 签名工具 - Windows
    - name: Install Cosign (Windows)
      if: matrix.os == 'windows-latest'
      run: |
        echo "Installing cosign for Windows..."
        curl -L -o cosign.exe "https://github.com/sigstore/cosign/releases/latest/download/cosign-windows-amd64.exe"
        mkdir -p "${{ github.workspace }}/bin"
        mv cosign.exe "${{ github.workspace }}/bin/"
        echo "${{ github.workspace }}/bin" >> $GITHUB_PATH
        echo "Cosign installed successfully"
      shell: bash

    # 安装 Sigstore 签名工具 - macOS
    - name: Install Cosign (macOS)
      if: matrix.os == 'macos-latest'
      run: |
        echo "Installing cosign for macOS..."
        brew install cosign
        echo "Cosign installed successfully"

    # 安装 Sigstore 签名工具 - Linux
    - name: Install Cosign (Linux)
      if: matrix.os == 'ubuntu-latest'
      run: |
        echo "Installing cosign for Linux..."
        curl -L -o cosign "https://github.com/sigstore/cosign/releases/latest/download/cosign-linux-amd64"
        chmod +x cosign
        sudo mv cosign /usr/local/bin/
        echo "Cosign installed successfully"

    - name: Build Electron application
      run: |
        cd client
        npm run build
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        BUILD_DATE: ${{ steps.date.outputs.date }}

    # 列出构建产物用于调试
    - name: List build artifacts
      run: |
        echo "Listing build artifacts..."
        ls -la client/dist/ || echo "No dist directory found"
      shell: bash

    # 使用 Sigstore 对构建产物进行签名 - Windows
    - name: Sign artifacts with Sigstore (Windows)
      if: matrix.os == 'windows-latest'
      run: |
        cd client/dist
        echo "Signing Windows artifacts..."

        # 检查并签名 .exe 文件
        for file in *.exe; do
          if [ -f "$file" ] && [ "$file" != "*.exe" ]; then
            echo "Signing $file with Sigstore..."
            cosign sign-blob --yes "$file" --output-signature "${file}.sig" --output-certificate "${file}.crt" || echo "Failed to sign $file"
            echo "✅ Signed: $file"
          fi
        done

        # 检查并签名 .msi 文件
        for file in *.msi; do
          if [ -f "$file" ] && [ "$file" != "*.msi" ]; then
            echo "Signing $file with Sigstore..."
            cosign sign-blob --yes "$file" --output-signature "${file}.sig" --output-certificate "${file}.crt" || echo "Failed to sign $file"
            echo "✅ Signed: $file"
          fi
        done
      shell: bash
      env:
        COSIGN_EXPERIMENTAL: 1
      continue-on-error: true

    # 使用 Sigstore 对构建产物进行签名 - macOS
    - name: Sign artifacts with Sigstore (macOS)
      if: matrix.os == 'macos-latest'
      run: |
        cd client/dist
        echo "Signing macOS artifacts..."

        # 检查并签名 .dmg 文件
        for file in *.dmg; do
          if [ -f "$file" ] && [ "$file" != "*.dmg" ]; then
            echo "Signing $file with Sigstore..."
            cosign sign-blob --yes "$file" --output-signature "${file}.sig" --output-certificate "${file}.crt" || echo "Failed to sign $file"
            echo "✅ Signed: $file"
          fi
        done

        # 检查并签名 .zip 文件
        for file in *.zip; do
          if [ -f "$file" ] && [ "$file" != "*.zip" ]; then
            echo "Signing $file with Sigstore..."
            cosign sign-blob --yes "$file" --output-signature "${file}.sig" --output-certificate "${file}.crt" || echo "Failed to sign $file"
            echo "✅ Signed: $file"
          fi
        done
      shell: bash
      env:
        COSIGN_EXPERIMENTAL: 1
      continue-on-error: true

    # 使用 Sigstore 对构建产物进行签名 - Linux
    - name: Sign artifacts with Sigstore (Linux)
      if: matrix.os == 'ubuntu-latest'
      run: |
        cd client/dist
        echo "Signing Linux artifacts..."

        # 检查并签名 .AppImage 文件
        for file in *.AppImage; do
          if [ -f "$file" ] && [ "$file" != "*.AppImage" ]; then
            echo "Signing $file with Sigstore..."
            cosign sign-blob --yes "$file" --output-signature "${file}.sig" --output-certificate "${file}.crt" || echo "Failed to sign $file"
            echo "✅ Signed: $file"
          fi
        done

        # 检查并签名 .deb 文件
        for file in *.deb; do
          if [ -f "$file" ] && [ "$file" != "*.deb" ]; then
            echo "Signing $file with Sigstore..."
            cosign sign-blob --yes "$file" --output-signature "${file}.sig" --output-certificate "${file}.crt" || echo "Failed to sign $file"
            echo "✅ Signed: $file"
          fi
        done
      shell: bash
      env:
        COSIGN_EXPERIMENTAL: 1
      continue-on-error: true

    - name: Upload artifacts (Windows)
      if: matrix.os == 'windows-latest'
      uses: actions/upload-artifact@v4
      with:
        name: windows-build
        path: |
          client/dist/
        if-no-files-found: warn

    - name: Upload artifacts (macOS)
      if: matrix.os == 'macos-latest'
      uses: actions/upload-artifact@v4
      with:
        name: macos-build
        path: |
          client/dist/
        if-no-files-found: warn

    - name: Upload artifacts (Linux)
      if: matrix.os == 'ubuntu-latest'
      uses: actions/upload-artifact@v4
      with:
        name: linux-build
        path: |
          client/dist/
        if-no-files-found: warn

  release:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download all artifacts
      uses: actions/download-artifact@v4

    - name: Get package version
      id: package-version
      run: echo "version=$(node -p "require('./client/package.json').version")" >> $GITHUB_OUTPUT

    - name: List downloaded artifacts
      run: |
        echo "Listing all downloaded artifacts..."
        find . -name "*.exe" -o -name "*.msi" -o -name "*.dmg" -o -name "*.zip" -o -name "*.AppImage" -o -name "*.deb" -o -name "*.yml" | head -20

    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        files: |
          windows-build/**
          macos-build/**
          linux-build/**
        draft: false
        prerelease: false
        generate_release_notes: true
        fail_on_unmatched_files: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Upload artifacts to server via HTTP API
      run: |
        API_UPLOAD_URL="https://aitools.vvvlin.com/api/releases/upload"
        API_KEY="8F1A3C5E7B9D2F4A6C8E0B1D3F5A7C9E1B3D5F7A9C1E3B5D7F9A1C3E5B7D9F1"
        echo "Uploading artifacts to $API_UPLOAD_URL"

        # 上传函数
        upload_files() {
          local dir="$1"
          if [ -d "$dir" ]; then
            echo "Processing directory: $dir"
            find "$dir" -type f \( -name "*.exe" -o -name "*.msi" -o -name "*.dmg" -o -name "*.zip" -o -name "*.AppImage" -o -name "*.deb" -o -name "*.yml" \) | while read -r file; do
              echo "Uploading $file"
              curl -sS -X POST "$API_UPLOAD_URL" \
                -H "x-api-key: $API_KEY" \
                -F "file=@$file" \
                || echo "Failed to upload $file"
            done
          else
            echo "Directory $dir not found"
          fi
        }

        # 上传各平台文件
        upload_files "windows-build"
        upload_files "macos-build"
        upload_files "linux-build"
      continue-on-error: true


