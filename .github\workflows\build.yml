name: Build and Release

on:
  push:
    tags: [ 'v*' ]

permissions:
  contents: write
  packages: read
  id-token: write

jobs:
  build:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install client dependencies
      run: |
        cd client
        npm install --legacy-peer-deps

    - name: Get current date
      id: date
      run: echo "date=$(date +'%Y-%m-%d')" >> $GITHUB_OUTPUT
      shell: bash

    - name: Install Cosign (Windows)
      if: matrix.os == 'windows-latest'
      run: |
        curl -L -o cosign.exe "https://github.com/sigstore/cosign/releases/latest/download/cosign-windows-amd64.exe"
        mkdir -p "${{ github.workspace }}/bin"
        mv cosign.exe "${{ github.workspace }}/bin/"
        echo "${{ github.workspace }}/bin" >> $GITHUB_PATH
      shell: bash

    - name: Install Cosign (macOS)
      if: matrix.os == 'macos-latest'
      run: brew install cosign

    - name: Install Cosign (Linux)
      if: matrix.os == 'ubuntu-latest'
      run: |
        curl -L -o cosign "https://github.com/sigstore/cosign/releases/latest/download/cosign-linux-amd64"
        chmod +x cosign
        sudo mv cosign /usr/local/bin/

    - name: Build Electron application
      run: |
        cd client
        npm run build
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        BUILD_DATE: ${{ steps.date.outputs.date }}

    - name: Sign artifacts with Sigstore (Windows)
      if: matrix.os == 'windows-latest'
      run: |
        cd client/dist
        for file in *.exe *.msi; do
          if [ -f "$file" ] && [ "$file" != "*.exe" ] && [ "$file" != "*.msi" ]; then
            cosign sign-blob --yes "$file" --output-signature "${file}.sig" --output-certificate "${file}.crt" || true
          fi
        done
      shell: bash
      env:
        COSIGN_EXPERIMENTAL: 1
      continue-on-error: true

    - name: Sign artifacts with Sigstore (macOS)
      if: matrix.os == 'macos-latest'
      run: |
        cd client/dist
        for file in *.dmg *.zip; do
          if [ -f "$file" ] && [ "$file" != "*.dmg" ] && [ "$file" != "*.zip" ]; then
            cosign sign-blob --yes "$file" --output-signature "${file}.sig" --output-certificate "${file}.crt" || true
          fi
        done
      shell: bash
      env:
        COSIGN_EXPERIMENTAL: 1
      continue-on-error: true

    - name: Sign artifacts with Sigstore (Linux)
      if: matrix.os == 'ubuntu-latest'
      run: |
        cd client/dist
        for file in *.AppImage *.deb; do
          if [ -f "$file" ] && [ "$file" != "*.AppImage" ] && [ "$file" != "*.deb" ]; then
            cosign sign-blob --yes "$file" --output-signature "${file}.sig" --output-certificate "${file}.crt" || true
          fi
        done
      shell: bash
      env:
        COSIGN_EXPERIMENTAL: 1
      continue-on-error: true

    - name: Upload artifacts (Windows)
      if: matrix.os == 'windows-latest'
      uses: actions/upload-artifact@v4
      with:
        name: windows-build
        path: client/dist/

    - name: Upload artifacts (macOS)
      if: matrix.os == 'macos-latest'
      uses: actions/upload-artifact@v4
      with:
        name: macos-build
        path: client/dist/

    - name: Upload artifacts (Linux)
      if: matrix.os == 'ubuntu-latest'
      uses: actions/upload-artifact@v4
      with:
        name: linux-build
        path: client/dist/

  release:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download all artifacts
      uses: actions/download-artifact@v4

    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        files: |
          windows-build/**
          macos-build/**
          linux-build/**
        draft: false
        prerelease: false
        generate_release_notes: true
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Upload artifacts to server via HTTP API
      run: |
        API_UPLOAD_URL="https://aitools.vvvlin.com/api/releases/upload"
        API_KEY="8F1A3C5E7B9D2F4A6C8E0B1D3F5A7C9E1B3D5F7A9C1E3B5D7F9A1C3E5B7D9F1"

        upload_files() {
          local dir="$1"
          if [ -d "$dir" ]; then
            find "$dir" -type f \( -name "*.exe" -o -name "*.msi" -o -name "*.dmg" -o -name "*.zip" -o -name "*.AppImage" -o -name "*.deb" -o -name "*.yml" \) | while read -r file; do
              curl -sS -X POST "$API_UPLOAD_URL" \
                -H "x-api-key: $API_KEY" \
                -F "file=@$file" || true
            done
          fi
        }

        upload_files "windows-build"
        upload_files "macos-build"
        upload_files "linux-build"
      continue-on-error: true