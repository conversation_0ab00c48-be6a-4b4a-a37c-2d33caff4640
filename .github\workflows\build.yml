name: Build and Release

on:
  push:
    tags: [ 'v*' ]

permissions:
  contents: write
  packages: read
  id-token: write  # 为 Sigstore 签名添加权限

jobs:
  build:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install client dependencies
      run: |
        cd client
        npm install --legacy-peer-deps

    - name: Get current date
      id: date
      run: echo "date=$(date +'%Y-%m-%d')" >> $GITHUB_OUTPUT

    # 安装 Sigstore 签名工具
    - name: Install Sigstore
      run: |
        if [ "${{ matrix.os }}" = "windows-latest" ]; then
          # Windows: 下载 cosign
          curl -O -L "https://github.com/sigstore/cosign/releases/latest/download/cosign-windows-amd64.exe"
          mv cosign-windows-amd64.exe cosign.exe
          chmod +x cosign.exe
          echo "$(pwd)" >> $GITHUB_PATH
        elif [ "${{ matrix.os }}" = "macos-latest" ]; then
          # macOS: 使用 Homebrew 安装
          brew install cosign
        else
          # Linux: 下载二进制文件
          curl -O -L "https://github.com/sigstore/cosign/releases/latest/download/cosign-linux-amd64"
          mv cosign-linux-amd64 cosign
          chmod +x cosign
          sudo mv cosign /usr/local/bin/
        fi
      shell: bash

    - name: Build Electron application
      run: |
        cd client
        npm run build
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        BUILD_DATE: ${{ steps.date.outputs.date }}

    # 使用 Sigstore 对构建产物进行签名
    - name: Sign artifacts with Sigstore
      run: |
        cd client/dist

        # 为每个平台的文件进行签名
        if [ "${{ matrix.os }}" = "windows-latest" ]; then
          # Windows 文件签名
          for file in *.exe *.msi; do
            if [ -f "$file" ]; then
              echo "Signing $file with Sigstore..."
              cosign sign-blob --yes "$file" --output-signature "${file}.sig" --output-certificate "${file}.crt"
              echo "✅ Signed: $file"
            fi
          done
        elif [ "${{ matrix.os }}" = "macos-latest" ]; then
          # macOS 文件签名
          for file in *.dmg *.zip; do
            if [ -f "$file" ]; then
              echo "Signing $file with Sigstore..."
              cosign sign-blob --yes "$file" --output-signature "${file}.sig" --output-certificate "${file}.crt"
              echo "✅ Signed: $file"
            fi
          done
        else
          # Linux 文件签名
          for file in *.AppImage *.deb; do
            if [ -f "$file" ]; then
              echo "Signing $file with Sigstore..."
              cosign sign-blob --yes "$file" --output-signature "${file}.sig" --output-certificate "${file}.crt"
              echo "✅ Signed: $file"
            fi
          done
        fi
      shell: bash
      env:
        COSIGN_EXPERIMENTAL: 1

    - name: Upload artifacts (Windows)
      if: matrix.os == 'windows-latest'
      uses: actions/upload-artifact@v4
      with:
        name: windows-build
        path: |
          client/dist/*.exe
          client/dist/*.msi
          client/dist/*.sig
          client/dist/*.crt
          client/dist/latest.yml

    - name: Upload artifacts (macOS)
      if: matrix.os == 'macos-latest'
      uses: actions/upload-artifact@v4
      with:
        name: macos-build
        path: |
          client/dist/*.dmg
          client/dist/*.zip
          client/dist/*.sig
          client/dist/*.crt
          client/dist/latest-mac.yml

    - name: Upload artifacts (Linux)
      if: matrix.os == 'ubuntu-latest'
      uses: actions/upload-artifact@v4
      with:
        name: linux-build
        path: |
          client/dist/*.AppImage
          client/dist/*.deb
          client/dist/*.sig
          client/dist/*.crt
          client/dist/latest-linux.yml

  release:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download all artifacts
      uses: actions/download-artifact@v4

    - name: Get package version
      id: package-version
      run: echo "version=$(node -p "require('./client/package.json').version")" >> $GITHUB_OUTPUT

    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        files: |
          windows-build/*
          macos-build/*
          linux-build/*
        draft: false
        prerelease: false
        generate_release_notes: true
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}


    - name: Upload artifacts to server via HTTP API
      run: |
        set -e
        API_UPLOAD_URL="https://aitools.vvvlin.com/api/releases/upload"
        API_KEY="8F1A3C5E7B9D2F4A6C8E0B1D3F5A7C9E1B3D5F7A9C1E3B5D7F9A1C3E5B7D9F1"
        echo "Uploading artifacts to $API_UPLOAD_URL"
        # Windows
        if [ -d "windows-build" ]; then
          for f in windows-build/*; do
            [ -f "$f" ] || continue
            echo "Uploading $f"
            curl -sS -X POST "$API_UPLOAD_URL" \
              -H "x-api-key: $API_KEY" \
              -F "file=@$f" \
              || exit 1
          done
        fi
        # macOS
        if [ -d "macos-build" ]; then
          for f in macos-build/*; do
            [ -f "$f" ] || continue
            echo "Uploading $f"
            curl -sS -X POST "$API_UPLOAD_URL" \
              -H "x-api-key: $API_KEY" \
              -F "file=@$f" \
              || exit 1
          done
        fi
        # Linux
        if [ -d "linux-build" ]; then
          for f in linux-build/*; do
            [ -f "$f" ] || continue
            echo "Uploading $f"
            curl -sS -X POST "$API_UPLOAD_URL" \
              -H "x-api-key: $API_KEY" \
              -F "file=@$f" \
              || exit 1
          done
        fi


