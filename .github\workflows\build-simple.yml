name: Build and Release (Simple)

on:
  push:
    tags: [ 'v*' ]
  workflow_dispatch:  # 允许手动触发

permissions:
  contents: write
  packages: read
  id-token: write

jobs:
  build:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install client dependencies
      run: |
        cd client
        npm install --legacy-peer-deps

    - name: Get current date
      id: date
      run: echo "date=$(date +'%Y-%m-%d')" >> $GITHUB_OUTPUT
      shell: bash

    - name: Build Electron application
      run: |
        cd client
        npm run build
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        BUILD_DATE: ${{ steps.date.outputs.date }}

    - name: List build artifacts
      run: |
        echo "Listing build artifacts..."
        ls -la client/dist/ || echo "No dist directory found"
        echo "Files in dist:"
        find client/dist -type f -name "*.exe" -o -name "*.msi" -o -name "*.dmg" -o -name "*.zip" -o -name "*.AppImage" -o -name "*.deb" -o -name "*.yml" || echo "No matching files found"
      shell: bash

    - name: Upload artifacts (Windows)
      if: matrix.os == 'windows-latest'
      uses: actions/upload-artifact@v4
      with:
        name: windows-build
        path: client/dist/
        if-no-files-found: warn

    - name: Upload artifacts (macOS)
      if: matrix.os == 'macos-latest'
      uses: actions/upload-artifact@v4
      with:
        name: macos-build
        path: client/dist/
        if-no-files-found: warn

    - name: Upload artifacts (Linux)
      if: matrix.os == 'ubuntu-latest'
      uses: actions/upload-artifact@v4
      with:
        name: linux-build
        path: client/dist/
        if-no-files-found: warn

  release:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download all artifacts
      uses: actions/download-artifact@v4

    - name: List downloaded artifacts
      run: |
        echo "Listing all downloaded artifacts..."
        find . -type f -name "*.exe" -o -name "*.msi" -o -name "*.dmg" -o -name "*.zip" -o -name "*.AppImage" -o -name "*.deb" -o -name "*.yml" | head -20

    - name: Get package version
      id: package-version
      run: echo "version=$(node -p "require('./client/package.json').version")" >> $GITHUB_OUTPUT

    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        files: |
          windows-build/**
          macos-build/**
          linux-build/**
        draft: false
        prerelease: false
        generate_release_notes: true
        fail_on_unmatched_files: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Upload artifacts to server via HTTP API
      run: |
        API_UPLOAD_URL="https://aitools.vvvlin.com/api/releases/upload"
        API_KEY="8F1A3C5E7B9D2F4A6C8E0B1D3F5A7C9E1B3D5F7A9C1E3B5D7F9A1C3E5B7D9F1"
        echo "Uploading artifacts to $API_UPLOAD_URL"
        
        # 上传函数
        upload_files() {
          local dir="$1"
          if [ -d "$dir" ]; then
            echo "Processing directory: $dir"
            find "$dir" -type f \( -name "*.exe" -o -name "*.msi" -o -name "*.dmg" -o -name "*.zip" -o -name "*.AppImage" -o -name "*.deb" -o -name "*.yml" \) | while read -r file; do
              echo "Uploading $file"
              curl -sS -X POST "$API_UPLOAD_URL" \
                -H "x-api-key: $API_KEY" \
                -F "file=@$file" \
                || echo "Failed to upload $file"
            done
          else
            echo "Directory $dir not found"
          fi
        }
        
        # 上传各平台文件
        upload_files "windows-build"
        upload_files "macos-build"
        upload_files "linux-build"
      continue-on-error: true
