#!/usr/bin/env node

/**
 * 构建诊断脚本
 * 检查构建环境和依赖，帮助诊断构建问题
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

/**
 * 执行命令并返回结果
 */
function executeCommand(command, args = []) {
  return new Promise((resolve) => {
    const child = spawn(command, args, { stdio: 'pipe', shell: true });
    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      resolve({ code, stdout, stderr });
    });

    child.on('error', (error) => {
      resolve({ code: -1, stdout: '', stderr: error.message });
    });
  });
}

/**
 * 检查文件是否存在
 */
function checkFile(filePath, description) {
  const exists = fs.existsSync(filePath);
  console.log(`${exists ? '✅' : '❌'} ${description}: ${filePath}`);
  return exists;
}

/**
 * 检查目录是否存在
 */
function checkDirectory(dirPath, description) {
  const exists = fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory();
  console.log(`${exists ? '✅' : '❌'} ${description}: ${dirPath}`);
  return exists;
}

/**
 * 检查 Node.js 和 npm 版本
 */
async function checkNodeEnvironment() {
  console.log('\n🔍 检查 Node.js 环境...');
  
  const nodeResult = await executeCommand('node', ['--version']);
  if (nodeResult.code === 0) {
    console.log(`✅ Node.js 版本: ${nodeResult.stdout.trim()}`);
  } else {
    console.log(`❌ Node.js 未安装或不可用`);
  }

  const npmResult = await executeCommand('npm', ['--version']);
  if (npmResult.code === 0) {
    console.log(`✅ npm 版本: ${npmResult.stdout.trim()}`);
  } else {
    console.log(`❌ npm 未安装或不可用`);
  }
}

/**
 * 检查项目文件结构
 */
function checkProjectStructure() {
  console.log('\n📁 检查项目文件结构...');
  
  const clientDir = path.resolve('.');
  console.log(`当前目录: ${clientDir}`);
  
  // 检查关键文件
  checkFile('package.json', 'package.json');
  checkFile('src/main/main.js', '主进程文件');
  checkFile('src/renderer/index.html', '渲染进程文件');
  checkFile('assets/icon.ico', 'Windows 图标');
  checkFile('assets/icon.icns', 'macOS 图标');
  checkFile('assets/icon.png', 'Linux 图标');
  
  // 检查关键目录
  checkDirectory('src', '源代码目录');
  checkDirectory('assets', '资源目录');
  checkDirectory('node_modules', 'node_modules 目录');
  checkDirectory('scripts', '脚本目录');
}

/**
 * 检查依赖
 */
async function checkDependencies() {
  console.log('\n📦 检查依赖...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    console.log(`应用名称: ${packageJson.name}`);
    console.log(`应用版本: ${packageJson.version}`);
    
    // 检查关键依赖
    const devDeps = packageJson.devDependencies || {};
    const deps = packageJson.dependencies || {};
    
    console.log('\n开发依赖:');
    Object.keys(devDeps).forEach(dep => {
      console.log(`  - ${dep}: ${devDeps[dep]}`);
    });
    
    console.log('\n生产依赖:');
    Object.keys(deps).forEach(dep => {
      console.log(`  - ${dep}: ${deps[dep]}`);
    });
    
    // 检查 electron-builder 是否安装
    const electronBuilderResult = await executeCommand('npx', ['electron-builder', '--version']);
    if (electronBuilderResult.code === 0) {
      console.log(`✅ electron-builder 版本: ${electronBuilderResult.stdout.trim()}`);
    } else {
      console.log(`❌ electron-builder 未安装或不可用`);
      console.log(`错误: ${electronBuilderResult.stderr}`);
    }
    
  } catch (error) {
    console.log(`❌ 无法读取 package.json: ${error.message}`);
  }
}

/**
 * 检查构建配置
 */
function checkBuildConfiguration() {
  console.log('\n⚙️  检查构建配置...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const buildConfig = packageJson.build;
    
    if (buildConfig) {
      console.log('✅ 找到构建配置');
      console.log(`  - appId: ${buildConfig.appId}`);
      console.log(`  - productName: ${buildConfig.productName}`);
      console.log(`  - 输出目录: ${buildConfig.directories?.output || 'dist'}`);
      
      // 检查平台配置
      if (buildConfig.win) {
        console.log('✅ Windows 构建配置存在');
      }
      if (buildConfig.mac) {
        console.log('✅ macOS 构建配置存在');
      }
      if (buildConfig.linux) {
        console.log('✅ Linux 构建配置存在');
      }
      
    } else {
      console.log('❌ 未找到构建配置');
    }
    
  } catch (error) {
    console.log(`❌ 检查构建配置失败: ${error.message}`);
  }
}

/**
 * 测试构建命令
 */
async function testBuildCommands() {
  console.log('\n🧪 测试构建命令...');
  
  // 测试 electron 是否可以启动
  console.log('测试 electron 命令...');
  const electronResult = await executeCommand('npx', ['electron', '--version']);
  if (electronResult.code === 0) {
    console.log(`✅ Electron 版本: ${electronResult.stdout.trim()}`);
  } else {
    console.log(`❌ Electron 不可用: ${electronResult.stderr}`);
  }
  
  // 测试构建脚本
  console.log('测试构建脚本...');
  const buildScriptExists = fs.existsSync('scripts/build-with-date.js');
  console.log(`${buildScriptExists ? '✅' : '❌'} 构建脚本存在`);
}

/**
 * 提供修复建议
 */
function provideSuggestions() {
  console.log('\n💡 修复建议:');
  
  console.log('1. 如果依赖缺失，运行:');
  console.log('   npm install --legacy-peer-deps');
  
  console.log('\n2. 如果图标文件缺失，请确保 assets 目录包含:');
  console.log('   - icon.ico (Windows)');
  console.log('   - icon.icns (macOS)');
  console.log('   - icon.png (Linux)');
  
  console.log('\n3. 如果构建失败，尝试清理并重新安装:');
  console.log('   rm -rf node_modules package-lock.json');
  console.log('   npm install --legacy-peer-deps');
  
  console.log('\n4. 如果在 GitHub Actions 中失败，检查:');
  console.log('   - Node.js 版本是否正确 (推荐 18)');
  console.log('   - 是否有足够的权限');
  console.log('   - 网络连接是否正常');
  
  console.log('\n5. 本地测试构建:');
  console.log('   npm run build:win    # Windows');
  console.log('   npm run build:mac    # macOS');
  console.log('   npm run build:linux  # Linux');
}

/**
 * 主函数
 */
async function main() {
  console.log('🔧 AI重器 构建诊断工具\n');
  
  try {
    await checkNodeEnvironment();
    checkProjectStructure();
    await checkDependencies();
    checkBuildConfiguration();
    await testBuildCommands();
    provideSuggestions();
    
    console.log('\n✅ 诊断完成！');
    
  } catch (error) {
    console.error('❌ 诊断过程中出错:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  checkNodeEnvironment,
  checkProjectStructure,
  checkDependencies,
  checkBuildConfiguration
};
