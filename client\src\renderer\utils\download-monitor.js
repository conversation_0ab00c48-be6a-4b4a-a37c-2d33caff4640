/**
 * 下载监控工具
 * 监控更新下载状态，提供更好的用户体验
 */

const { ipc<PERSON><PERSON><PERSON> } = require('electron');

class DownloadMonitor {
  constructor() {
    this.isMonitoring = false;
    this.monitorInterval = null;
    this.lastProgress = null;
    this.stallDetectionTime = 30000; // 30秒无进度认为卡住
    this.callbacks = {
      onProgress: null,
      onStalled: null,
      onResumed: null,
      onError: null,
      onComplete: null
    };
  }

  /**
   * 开始监控下载
   * @param {Object} callbacks - 回调函数
   */
  startMonitoring(callbacks = {}) {
    this.callbacks = { ...this.callbacks, ...callbacks };
    this.isMonitoring = true;
    
    console.log('开始监控下载状态...');
    
    // 监听下载进度事件
    ipcRenderer.on('update-download-progress', this.handleProgress.bind(this));
    ipcRenderer.on('update-downloaded', this.handleComplete.bind(this));
    ipcRenderer.on('update-error', this.handleError.bind(this));
    
    // 定期检查下载状态
    this.monitorInterval = setInterval(() => {
      this.checkDownloadState();
    }, 5000); // 每5秒检查一次
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    this.isMonitoring = false;
    
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
    
    // 移除事件监听
    ipcRenderer.removeAllListeners('update-download-progress');
    ipcRenderer.removeAllListeners('update-downloaded');
    ipcRenderer.removeAllListeners('update-error');
    
    console.log('停止监控下载状态');
  }

  /**
   * 处理下载进度
   */
  handleProgress(event, progress) {
    const now = Date.now();
    
    // 检查是否从头开始下载（进度重置）
    if (this.lastProgress && progress.percent < this.lastProgress.percent) {
      console.warn('检测到下载重新开始，进度从', this.lastProgress.percent, '重置到', progress.percent);
      if (this.callbacks.onResumed) {
        this.callbacks.onResumed({
          type: 'restart',
          message: '下载重新开始',
          progress: progress
        });
      }
    }
    
    this.lastProgress = {
      ...progress,
      timestamp: now
    };
    
    if (this.callbacks.onProgress) {
      this.callbacks.onProgress(progress);
    }
  }

  /**
   * 处理下载完成
   */
  handleComplete(event, info) {
    console.log('下载完成:', info);
    this.stopMonitoring();
    
    if (this.callbacks.onComplete) {
      this.callbacks.onComplete(info);
    }
  }

  /**
   * 处理下载错误
   */
  handleError(event, error) {
    console.error('下载错误:', error);
    
    if (this.callbacks.onError) {
      this.callbacks.onError(error);
    }
  }

  /**
   * 检查下载状态
   */
  async checkDownloadState() {
    if (!this.isMonitoring) return;
    
    try {
      const state = await ipcRenderer.invoke('get-download-state');
      const now = Date.now();
      
      // 检查是否卡住
      if (state.isDownloading && this.lastProgress) {
        const timeSinceLastProgress = now - this.lastProgress.timestamp;
        
        if (timeSinceLastProgress > this.stallDetectionTime) {
          console.warn('检测到下载卡住，已', timeSinceLastProgress / 1000, '秒无进度');
          
          if (this.callbacks.onStalled) {
            this.callbacks.onStalled({
              stallTime: timeSinceLastProgress,
              lastProgress: this.lastProgress,
              state: state
            });
          }
        }
      }
      
    } catch (error) {
      console.error('检查下载状态失败:', error);
    }
  }

  /**
   * 手动重试下载
   */
  async retryDownload() {
    try {
      console.log('手动重试下载...');
      const result = await ipcRenderer.invoke('retry-update-download');
      
      if (result.success) {
        console.log('重试下载启动成功');
        // 重新开始监控
        if (!this.isMonitoring) {
          this.startMonitoring(this.callbacks);
        }
      } else {
        console.error('重试下载失败:', result.error);
        if (this.callbacks.onError) {
          this.callbacks.onError({
            message: result.error,
            isNetworkError: result.isNetworkError
          });
        }
      }
      
      return result;
    } catch (error) {
      console.error('重试下载异常:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 清理下载
   */
  async cleanupDownload() {
    try {
      const result = await ipcRenderer.invoke('cleanup-download');
      this.stopMonitoring();
      this.lastProgress = null;
      return result;
    } catch (error) {
      console.error('清理下载失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取当前下载状态
   */
  async getDownloadState() {
    try {
      return await ipcRenderer.invoke('get-download-state');
    } catch (error) {
      console.error('获取下载状态失败:', error);
      return null;
    }
  }

  /**
   * 格式化字节数
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 格式化速度
   */
  formatSpeed(bytesPerSecond) {
    return this.formatBytes(bytesPerSecond) + '/s';
  }

  /**
   * 计算剩余时间
   */
  calculateETA(transferred, total, bytesPerSecond) {
    if (bytesPerSecond <= 0) return '未知';
    
    const remaining = total - transferred;
    const seconds = Math.round(remaining / bytesPerSecond);
    
    if (seconds < 60) return `${seconds}秒`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}分钟`;
    return `${Math.round(seconds / 3600)}小时`;
  }
}

// 创建全局实例
const downloadMonitor = new DownloadMonitor();

// 导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { DownloadMonitor, downloadMonitor };
} else {
  window.DownloadMonitor = DownloadMonitor;
  window.downloadMonitor = downloadMonitor;
}
